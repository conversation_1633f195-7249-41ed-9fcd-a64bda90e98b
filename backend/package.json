{"name": "suplementor-backend", "version": "1.0.0", "description": "Backend API for Suplementor Knowledge Graph", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:research": "jest src/tests/research.test.ts", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,json}\"", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "docker:build": "docker build -t suplementor-backend .", "docker:run": "docker run -p 3000:3000 suplementor-backend", "setup:research": "chmod +x scripts/setup_research.sh && ./scripts/setup_research.sh", "cosmic:setup": "npm run setup:research && echo '🚀 COSMIC RESEARCH SYSTEM READY! 🚀'"}, "keywords": ["knowledge-graph", "supplements", "neo4j", "ai", "gemini", "rag", "typescript", "express"], "author": "Suplementor Team", "license": "MIT", "dependencies": {"@ag-ui/client": "^0.0.28", "@google/generative-ai": "^0.2.1", "@langchain/community": "^0.0.20", "@langchain/openai": "^0.0.14", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0-rc.12", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "eventsource": "^2.0.2", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "form-data": "^4.0.0", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "langchain": "^0.0.208", "lodash": "^4.17.21", "mammoth": "^1.6.0", "moment": "^2.29.4", "mongodb": "^6.3.0", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "neo4j-driver": "^5.15.0", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "ollama": "^0.5.0", "openai": "^4.20.1", "pdf-parse": "^1.1.1", "redis": "^4.6.10", "rxjs": "^7.8.1", "sharp": "^0.33.1", "socket.io": "^4.7.4", "uuid": "^9.0.1", "weaviate-ts-client": "^1.5.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "ws": "^8.14.2"}, "overrides": {"rxjs": "$rxjs"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/node-cron": "^3.0.11", "@types/pdf-parse": "^1.1.5", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "rimraf": "^5.0.5", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/index.ts"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "eslintConfig": {"parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "@typescript-eslint/recommended"], "env": {"node": true, "es2022": true}, "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/explicit-function-return-type": "warn", "no-console": "warn"}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": false}}