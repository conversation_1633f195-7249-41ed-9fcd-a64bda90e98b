import express from 'express';
import { WebSocket, WebSocketServer } from 'ws';
import { SupplementResearchAgent } from '../agents/SupplementResearchAgent';
import { logger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';

const router = express.Router();

// Store active agents and connections
const activeAgents = new Map<string, SupplementResearchAgent>();
const activeConnections = new Map<string, WebSocket>();

/**
 * Initialize WebSocket server for AG-UI protocol
 */
export function initializeAGUIWebSocket(server: any) {
  const wss = new WebSocketServer({ 
    server,
    path: '/agui/ws'
  });

  wss.on('connection', (ws: WebSocket, _request) => {
    const connectionId = uuidv4();
    activeConnections.set(connectionId, ws);
    
    logger.info(`AG-UI WebSocket connection established: ${connectionId}`);

    ws.on('message', async (data: Buffer) => {
      try {
        const message = JSON.parse(data.toString());
        await handleAGUIMessage(connectionId, message, ws);
      } catch (error) {
        logger.error('Error handling AG-UI message:', error);
        ws.send(JSON.stringify({
          type: 'error',
          message: 'Invalid message format'
        }));
      }
    });

    ws.on('close', () => {
      logger.info(`AG-UI WebSocket connection closed: ${connectionId}`);
      activeConnections.delete(connectionId);
      
      // Clean up any active agents for this connection
      for (const [agentId, _agent] of activeAgents.entries()) {
        if (agentId.startsWith(connectionId)) {
          activeAgents.delete(agentId);
        }
      }
    });

    ws.on('error', (error) => {
      logger.error(`AG-UI WebSocket error for ${connectionId}:`, error);
    });

    // Send welcome message
    ws.send(JSON.stringify({
      type: 'connection_established',
      connectionId,
      timestamp: new Date().toISOString()
    }));
  });

  return wss;
}

/**
 * Handle incoming AG-UI messages
 */
async function handleAGUIMessage(
  connectionId: string,
  message: any,
  ws: WebSocket
): Promise<void> {
  const { type, payload } = message;

  switch (type) {
    case 'start_supplement_research':
      await handleSupplementResearch(connectionId, payload, ws);
      break;
    
    case 'natural_language_query':
      await handleNaturalLanguageQuery(connectionId, payload, ws);
      break;
    
    case 'get_graph_state':
      await handleGetGraphState(connectionId, payload, ws);
      break;
    
    case 'ping':
      ws.send(JSON.stringify({ type: 'pong', timestamp: new Date().toISOString() }));
      break;
    
    default:
      ws.send(JSON.stringify({
        type: 'error',
        message: `Unknown message type: ${type}`
      }));
  }
}

/**
 * Handle supplement research requests
 */
async function handleSupplementResearch(
  connectionId: string,
  payload: any,
  ws: WebSocket
): Promise<void> {
  try {
    const { supplementName, researchDepth, includeInteractions } = payload;
    
    if (!supplementName) {
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Supplement name is required'
      }));
      return;
    }

    const agentId = `${connectionId}-${uuidv4()}`;
    const agent = new SupplementResearchAgent();
    activeAgents.set(agentId, agent);

    const runId = uuidv4();
    const threadId = uuidv4();

    // Start the research process
    const researchInput: any = {
      supplementName,
      researchDepth: researchDepth || 'basic',
      includeInteractions: includeInteractions || false,
      runId,
      threadId,
      messages: [],
      tools: [],
      context: [],
      state: {},
      agentId
    };

    // Subscribe to agent events and forward to WebSocket
    const subscription = agent.runSupplementResearch(researchInput).subscribe({
      next: (event) => {
        ws.send(JSON.stringify({
          type: 'agent_event',
          agentId,
          event
        }));
      },
      error: (error) => {
        logger.error(`Agent error for ${agentId}:`, error);
        ws.send(JSON.stringify({
          type: 'agent_error',
          agentId,
          error: error.message
        }));
        activeAgents.delete(agentId);
      },
      complete: () => {
        logger.info(`Agent completed for ${agentId}`);
        ws.send(JSON.stringify({
          type: 'agent_completed',
          agentId
        }));
        activeAgents.delete(agentId);
      }
    });

    // Store subscription for potential cleanup
    (agent as any).subscription = subscription;

  } catch (error) {
    logger.error('Error starting supplement research:', error);
    ws.send(JSON.stringify({
      type: 'error',
      message: 'Failed to start supplement research'
    }));
  }
}

/**
 * Handle natural language queries about the knowledge graph
 */
async function handleNaturalLanguageQuery(
  _connectionId: string,
  payload: any,
  ws: WebSocket
): Promise<void> {
  try {
    const { query, context } = payload;
    
    if (!query) {
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Query is required'
      }));
      return;
    }

    // Create a temporary agent for query processing
    const agent = new SupplementResearchAgent();
    const gemmaService = (agent as any).gemmaService;
    
    const response = await gemmaService.processNaturalLanguageQuery(query, context);
    
    ws.send(JSON.stringify({
      type: 'query_response',
      query,
      response,
      timestamp: new Date().toISOString()
    }));

  } catch (error) {
    logger.error('Error processing natural language query:', error);
    ws.send(JSON.stringify({
      type: 'error',
      message: 'Failed to process query'
    }));
  }
}

/**
 * Handle graph state requests
 */
async function handleGetGraphState(
  _connectionId: string,
  payload: any,
  ws: WebSocket
): Promise<void> {
  try {
    const { supplementName } = payload;
    
    // Create a temporary agent to access graph service
    const agent = new SupplementResearchAgent();
    const graphService = (agent as any).graphService;
    
    const graphData = await graphService.getKnowledgeGraph(supplementName);
    
    ws.send(JSON.stringify({
      type: 'graph_state',
      data: graphData,
      timestamp: new Date().toISOString()
    }));

  } catch (error) {
    logger.error('Error getting graph state:', error);
    ws.send(JSON.stringify({
      type: 'error',
      message: 'Failed to get graph state'
    }));
  }
}

/**
 * REST endpoints for AG-UI integration
 */

// Get available agents
router.get('/agents', (_req, res) => {
  res.json({
    agents: [
      {
        id: 'supplement-research',
        name: 'Supplement Research Agent',
        description: 'Researches supplements and builds knowledge graphs',
        capabilities: [
          'supplement_research',
          'knowledge_graph_building',
          'natural_language_queries'
        ]
      }
    ]
  });
});

// Get agent status
router.get('/agents/:agentId/status', (req, res) => {
  const { agentId } = req.params;
  const agent = activeAgents.get(agentId);

  if (!agent) {
    return res.status(404).json({ error: 'Agent not found' });
  }

  return res.json({
    agentId,
    status: 'active',
    timestamp: new Date().toISOString()
  });
});

// Health check endpoint
router.get('/health', async (_req, res) => {
  try {
    // Check if services are available
    const agent = new SupplementResearchAgent();
    const gemmaService = (agent as any).gemmaService;
    const graphService = (agent as any).graphService;
    
    const [gemmaHealth, graphHealth] = await Promise.all([
      gemmaService.healthCheck(),
      graphService.healthCheck()
    ]);
    
    res.json({
      status: 'healthy',
      services: {
        gemma: gemmaHealth,
        graph: graphHealth,
        websocket: true
      },
      activeConnections: activeConnections.size,
      activeAgents: activeAgents.size,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('AG-UI health check failed:', error);
    res.status(500).json({
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
