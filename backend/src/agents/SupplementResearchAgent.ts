import {
  AbstractAgent,
  RunAgentInput,
  EventType,
  BaseEvent,
} from '@ag-ui/client';
import { Observable } from 'rxjs';
import { ResearchService } from '../services/ResearchService';
import { GemmaService } from '../services/GemmaService';
import { GraphService } from '../services/GraphService';
import { logger } from '../utils/logger';

export interface SupplementResearchInput extends RunAgentInput {
  supplementName: string;
  researchDepth?: 'basic' | 'comprehensive';
  includeInteractions?: boolean;
}

export class SupplementResearchAgent extends AbstractAgent {
  private researchService: ResearchService;
  private gemmaService: GemmaService;
  private graphService: GraphService;

  constructor() {
    super();
    this.researchService = new ResearchService();
    this.gemmaService = new GemmaService();
    this.graphService = new GraphService();
  }

  protected run(input: RunAgentInput): Observable<BaseEvent> {
    const supplementInput = input as SupplementResearchInput;
    return new Observable<BaseEvent>((observer) => {
      this.executeResearchPipeline(supplementInput, observer);
    });
  }

  /**
   * Public method to run supplement research with custom input
   */
  public runSupplementResearch(input: SupplementResearchInput): Observable<BaseEvent> {
    return this.run(input);
  }

  private async executeResearchPipeline(
    input: SupplementResearchInput,
    observer: any
  ): Promise<void> {
    const messageId = Date.now().toString();
    
    try {
      // Start the research process
      observer.next({
        type: EventType.RUN_STARTED,
        threadId: input.threadId,
        runId: input.runId,
      });

      observer.next({
        type: EventType.TEXT_MESSAGE_START,
        messageId,
      });

      // Phase 1: Initial research gathering
      observer.next({
        type: EventType.TEXT_MESSAGE_CONTENT,
        messageId,
        delta: `🔍 Starting research for supplement: ${input.supplementName}\n`,
      });

      observer.next({
        type: EventType.TOOL_CALL_START,
        toolCallId: `research-${Date.now()}`,
        toolCallName: 'tavily_research',
        parentMessageId: messageId,
      });

      // Gather research data using Tavily MCP
      const researchData = await this.researchService.gatherSupplementResearch(
        input.supplementName,
        {
          includeInteractions: input.includeInteractions || false,
          researchDepth: input.researchDepth || 'basic'
        }
      );

      observer.next({
        type: EventType.TOOL_CALL_END,
        toolCallId: `research-${Date.now()}`,
        toolCallName: 'tavily_research',
        parentMessageId: messageId,
      });

      observer.next({
        type: EventType.TEXT_MESSAGE_CONTENT,
        messageId,
        delta: `✅ Gathered ${researchData.researchResults?.length || 0} research sources\n`,
      });

      // Phase 2: AI Analysis with Gemma
      observer.next({
        type: EventType.TOOL_CALL_START,
        toolCallId: `gemma-analysis-${Date.now()}`,
        toolCallName: 'gemma_analysis',
        parentMessageId: messageId,
      });

      const analysis = await this.gemmaService.analyzeSupplementData(
        input.supplementName,
        researchData
      );

      observer.next({
        type: EventType.TOOL_CALL_END,
        toolCallId: `gemma-analysis-${Date.now()}`,
        toolCallName: 'gemma_analysis',
        parentMessageId: messageId,
      });

      observer.next({
        type: EventType.TEXT_MESSAGE_CONTENT,
        messageId,
        delta: `🧠 AI Analysis complete. Identified ${analysis.healthDomains.length} health domains\n`,
      });

      // Phase 3: Knowledge Graph Update
      observer.next({
        type: EventType.TOOL_CALL_START,
        toolCallId: `graph-update-${Date.now()}`,
        toolCallName: 'update_knowledge_graph',
        parentMessageId: messageId,
      });

      const graphUpdate = await this.graphService.updateSupplementKnowledge(
        input.supplementName,
        analysis,
        researchData
      );

      observer.next({
        type: EventType.TOOL_CALL_END,
        toolCallId: `graph-update-${Date.now()}`,
        toolCallName: 'update_knowledge_graph',
        parentMessageId: messageId,
      });

      // Send graph state delta
      observer.next({
        type: EventType.STATE_DELTA,
        delta: {
          supplement: {
            name: input.supplementName,
            nodes: graphUpdate.createdNodes || 0,
            edges: graphUpdate.createdRelationships || 0,
            healthDomains: analysis.healthDomains,
            properties: analysis.properties,
          },
        },
      });

      observer.next({
        type: EventType.TEXT_MESSAGE_CONTENT,
        messageId,
        delta: `📊 Knowledge graph updated with ${graphUpdate.createdNodes || 0} nodes and ${graphUpdate.createdRelationships || 0} connections\n`,
      });

      // Phase 4: Generate insights
      const insights = await this.gemmaService.generateInsights(
        input.supplementName,
        analysis,
        graphUpdate
      );

      observer.next({
        type: EventType.TEXT_MESSAGE_CONTENT,
        messageId,
        delta: `\n💡 **Key Insights:**\n${insights.summary}\n\n`,
      });

      insights.keyFindings.forEach((finding, index) => {
        observer.next({
          type: EventType.TEXT_MESSAGE_CONTENT,
          messageId,
          delta: `${index + 1}. ${finding}\n`,
        });
      });

      if (insights.warnings.length > 0) {
        observer.next({
          type: EventType.TEXT_MESSAGE_CONTENT,
          messageId,
          delta: `\n⚠️ **Important Considerations:**\n`,
        });

        insights.warnings.forEach((warning, index) => {
          observer.next({
            type: EventType.TEXT_MESSAGE_CONTENT,
            messageId,
            delta: `${index + 1}. ${warning}\n`,
          });
        });
      }

      observer.next({
        type: EventType.TEXT_MESSAGE_CONTENT,
        messageId,
        delta: `\n🎯 Research complete! The knowledge graph has been updated with comprehensive information about ${input.supplementName}.`,
      });

      observer.next({
        type: EventType.TEXT_MESSAGE_END,
        messageId,
      });

      observer.next({
        type: EventType.RUN_FINISHED,
        threadId: input.threadId,
        runId: input.runId,
      });

      observer.complete();

    } catch (error) {
      logger.error('Error in supplement research pipeline:', error);
      
      observer.next({
        type: EventType.RUN_ERROR,
        message: `Research failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      });

      observer.error(error);
    }
  }
}
