import { createClient, RedisClientType } from 'redis';
import { config } from '@/config/environment';
import { logger, logDatabaseOperation, logError } from '@/utils/logger';

class RedisConnection {
  private client: RedisClientType | null = null;
  private isConnected = false;

  async connect(): Promise<void> {
    try {
      logger.info('Connecting to Redis cache...');
      
      // Create Redis client
      this.client = createClient({
        url: config.redis.url,
        password: config.redis.password || undefined,
        socket: {
          connectTimeout: 60000,
          reconnectStrategy: (retries) => {
            if (retries > 10) {
              logger.error('Redis reconnection failed after 10 attempts');
              return false;
            }
            return Math.min(retries * 50, 1000);
          },
        },
      });

      // Set up event listeners
      this.client.on('error', (error) => {
        logError('Redis client error', error);
      });

      this.client.on('connect', () => {
        logger.info('Redis client connected');
      });

      this.client.on('ready', () => {
        logger.info('Redis client ready');
        this.isConnected = true;
      });

      this.client.on('end', () => {
        logger.info('Redis client disconnected');
        this.isConnected = false;
      });

      this.client.on('reconnecting', () => {
        logger.info('Redis client reconnecting...');
      });

      // Connect to Redis
      await this.client.connect();
      
      logger.info('✅ Redis connection established successfully');
      
    } catch (error) {
      logError('❌ Failed to connect to Redis', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.quit();
      this.isConnected = false;
      logger.info('🔌 Redis connection closed');
    }
  }

  getClient(): RedisClientType {
    if (!this.client || !this.isConnected) {
      throw new Error('Redis client not initialized. Call connect() first.');
    }
    return this.client;
  }

  async set(key: string, value: any, ttl?: number): Promise<void> {
    const client = this.getClient();
    const startTime = Date.now();
    
    try {
      const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
      
      if (ttl) {
        await client.setEx(key, ttl, serializedValue);
      } else {
        await client.set(key, serializedValue);
      }
      
      const duration = Date.now() - startTime;
      logDatabaseOperation('SET', 'Redis', duration, { key, ttl });
    } catch (error) {
      logError('Redis SET failed', error, { key });
      throw error;
    }
  }

  async get(key: string): Promise<any> {
    const client = this.getClient();
    const startTime = Date.now();
    
    try {
      const value = await client.get(key);
      const duration = Date.now() - startTime;
      
      logDatabaseOperation('GET', 'Redis', duration, { key, found: !!value });
      
      if (!value) return null;
      
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    } catch (error) {
      logError('Redis GET failed', error, { key });
      throw error;
    }
  }

  async del(key: string | string[]): Promise<number> {
    const client = this.getClient();
    const startTime = Date.now();
    
    try {
      const result = await client.del(key);
      const duration = Date.now() - startTime;
      
      logDatabaseOperation('DEL', 'Redis', duration, { key, deleted: result });
      
      return result;
    } catch (error) {
      logError('Redis DEL failed', error, { key });
      throw error;
    }
  }

  async exists(key: string): Promise<boolean> {
    const client = this.getClient();
    
    try {
      const result = await client.exists(key);
      return result === 1;
    } catch (error) {
      logError('Redis EXISTS failed', error, { key });
      throw error;
    }
  }

  async expire(key: string, seconds: number): Promise<boolean> {
    const client = this.getClient();
    
    try {
      const result = await client.expire(key, seconds);
      return result;
    } catch (error) {
      logError('Redis EXPIRE failed', error, { key, seconds });
      throw error;
    }
  }

  async ttl(key: string): Promise<number> {
    const client = this.getClient();
    
    try {
      return await client.ttl(key);
    } catch (error) {
      logError('Redis TTL failed', error, { key });
      throw error;
    }
  }

  async keys(pattern: string): Promise<string[]> {
    const client = this.getClient();
    
    try {
      return await client.keys(pattern);
    } catch (error) {
      logError('Redis KEYS failed', error, { pattern });
      throw error;
    }
  }

  async flushAll(): Promise<void> {
    if (config.isProduction) {
      throw new Error('Cannot flush Redis in production environment');
    }
    
    const client = this.getClient();
    
    try {
      await client.flushAll();
      logger.warn('⚠️ Redis cache cleared');
    } catch (error) {
      logError('Redis FLUSHALL failed', error);
      throw error;
    }
  }

  // Hash operations
  async hSet(key: string, field: string, value: any): Promise<number> {
    const client = this.getClient();
    
    try {
      const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
      return await client.hSet(key, field, serializedValue);
    } catch (error) {
      logError('Redis HSET failed', error, { key, field });
      throw error;
    }
  }

  async hGet(key: string, field: string): Promise<any> {
    const client = this.getClient();
    
    try {
      const value = await client.hGet(key, field);
      if (!value) return null;
      
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    } catch (error) {
      logError('Redis HGET failed', error, { key, field });
      throw error;
    }
  }

  async hGetAll(key: string): Promise<Record<string, any>> {
    const client = this.getClient();
    
    try {
      const hash = await client.hGetAll(key);
      const result: Record<string, any> = {};
      
      for (const [field, value] of Object.entries(hash)) {
        try {
          result[field] = JSON.parse(value);
        } catch {
          result[field] = value;
        }
      }
      
      return result;
    } catch (error) {
      logError('Redis HGETALL failed', error, { key });
      throw error;
    }
  }

  async hDel(key: string, field: string | string[]): Promise<number> {
    const client = this.getClient();
    
    try {
      return await client.hDel(key, field);
    } catch (error) {
      logError('Redis HDEL failed', error, { key, field });
      throw error;
    }
  }

  // List operations
  async lPush(key: string, ...values: any[]): Promise<number> {
    const client = this.getClient();
    
    try {
      const serializedValues = values.map(v => typeof v === 'string' ? v : JSON.stringify(v));
      return await client.lPush(key, serializedValues);
    } catch (error) {
      logError('Redis LPUSH failed', error, { key });
      throw error;
    }
  }

  async lRange(key: string, start: number, stop: number): Promise<any[]> {
    const client = this.getClient();
    
    try {
      const values = await client.lRange(key, start, stop);
      return values.map(value => {
        try {
          return JSON.parse(value);
        } catch {
          return value;
        }
      });
    } catch (error) {
      logError('Redis LRANGE failed', error, { key, start, stop });
      throw error;
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      const client = this.getClient();
      const result = await client.ping();
      return result === 'PONG';
    } catch (error) {
      logError('Redis health check failed', error);
      return false;
    }
  }

  async getStats(): Promise<any> {
    try {
      const client = this.getClient();
      const info = await client.info();
      
      // Parse Redis info response
      const stats: any = {};
      const sections = info.split('\r\n\r\n');
      
      for (const section of sections) {
        const lines = section.split('\r\n');
        if (lines.length > 0 && lines[0]) {
          const sectionName = lines[0].replace('# ', '');
          stats[sectionName] = {};

          for (let i = 1; i < lines.length; i++) {
            const line = lines[i];
            if (line && line.includes(':')) {
              const [key, value] = line.split(':');
              if (sectionName && key) {
                stats[sectionName][key] = isNaN(Number(value)) ? value : Number(value);
              }
            }
          }
        }
      }
      
      return stats;
    } catch (error) {
      logError('Failed to get Redis stats', error);
      throw error;
    }
  }
}

// Create singleton instance
const redisConnection = new RedisConnection();

// Export connection functions
export const connectRedis = () => redisConnection.connect();
export const disconnectRedis = () => redisConnection.disconnect();
export const getRedisClient = () => redisConnection.getClient();

// Export cache operations
export const cacheSet = (key: string, value: any, ttl?: number) => redisConnection.set(key, value, ttl);
export const cacheGet = (key: string) => redisConnection.get(key);
export const cacheDel = (key: string | string[]) => redisConnection.del(key);
export const cacheExists = (key: string) => redisConnection.exists(key);
export const cacheExpire = (key: string, seconds: number) => redisConnection.expire(key, seconds);
export const cacheTtl = (key: string) => redisConnection.ttl(key);
export const cacheKeys = (pattern: string) => redisConnection.keys(pattern);
export const cacheFlushAll = () => redisConnection.flushAll();

// Export hash operations
export const cacheHSet = (key: string, field: string, value: any) => redisConnection.hSet(key, field, value);
export const cacheHGet = (key: string, field: string) => redisConnection.hGet(key, field);
export const cacheHGetAll = (key: string) => redisConnection.hGetAll(key);
export const cacheHDel = (key: string, field: string | string[]) => redisConnection.hDel(key, field);

// Export list operations
export const cacheLPush = (key: string, ...values: any[]) => redisConnection.lPush(key, ...values);
export const cacheLRange = (key: string, start: number, stop: number) => redisConnection.lRange(key, start, stop);

// Export health check
export const redisHealthCheck = () => redisConnection.healthCheck();
export const getRedisStats = () => redisConnection.getStats();

export default redisConnection;
