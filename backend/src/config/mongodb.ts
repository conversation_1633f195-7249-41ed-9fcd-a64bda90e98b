import mongoose from 'mongoose';
import { config } from '@/config/environment';
import { logger, logError } from '@/utils/logger';

class MongoDBConnection {
  private isConnected = false;

  async connect(): Promise<void> {
    try {
      logger.info('Connecting to MongoDB...');
      
      // Set mongoose options
      mongoose.set('strictQuery', false);
      
      // Connect to MongoDB
      await mongoose.connect(config.mongodb.uri, {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        bufferCommands: false,
      });

      this.isConnected = true;
      
      // Set up event listeners
      mongoose.connection.on('connected', () => {
        logger.info('✅ MongoDB connected successfully');
      });

      mongoose.connection.on('error', (error) => {
        logError('MongoDB connection error', error);
      });

      mongoose.connection.on('disconnected', () => {
        logger.warn('MongoDB disconnected');
        this.isConnected = false;
      });

      mongoose.connection.on('reconnected', () => {
        logger.info('MongoDB reconnected');
        this.isConnected = true;
      });

      // Graceful shutdown
      process.on('SIGINT', async () => {
        await this.disconnect();
        process.exit(0);
      });

      logger.info('✅ MongoDB connection established successfully');
      
    } catch (error) {
      logError('❌ Failed to connect to MongoDB', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.isConnected) {
      await mongoose.connection.close();
      this.isConnected = false;
      logger.info('🔌 MongoDB connection closed');
    }
  }

  getConnection(): typeof mongoose.connection {
    if (!this.isConnected) {
      throw new Error('MongoDB not connected. Call connect() first.');
    }
    return mongoose.connection;
  }

  async healthCheck(): Promise<boolean> {
    try {
      const state = mongoose.connection.readyState;
      return state === 1; // 1 = connected
    } catch (error) {
      logError('MongoDB health check failed', error);
      return false;
    }
  }

  async getStats(): Promise<any> {
    try {
      const db = mongoose.connection.db;
      if (!db) {
        throw new Error('Database not available');
      }

      const stats = await db.stats();
      const collections = await db.listCollections().toArray();
      
      const collectionStats = [];
      for (const collection of collections) {
        try {
          const collStats = await db.collection(collection.name).estimatedDocumentCount();
          collectionStats.push({
            name: collection.name,
            count: collStats,
            size: 0, // Not available with estimatedDocumentCount
            avgObjSize: 0, // Not available with estimatedDocumentCount
            indexes: 0, // Not available with estimatedDocumentCount
          });
        } catch (error) {
          // Some collections might not support stats
          collectionStats.push({
            name: collection.name,
            error: 'Stats not available',
          });
        }
      }

      return {
        database: {
          name: stats['db'],
          collections: stats['collections'],
          objects: stats['objects'],
          dataSize: stats['dataSize'],
          storageSize: stats['storageSize'],
          indexes: stats['indexes'],
          indexSize: stats['indexSize'],
        },
        collections: collectionStats,
      };
    } catch (error) {
      logError('Failed to get MongoDB stats', error);
      throw error;
    }
  }

  // Helper method to clear all data (use with caution!)
  async clearDatabase(): Promise<void> {
    if (config.isProduction) {
      throw new Error('Cannot clear database in production environment');
    }
    
    try {
      const db = mongoose.connection.db;
      if (!db) {
        throw new Error('Database not available');
      }

      const collections = await db.listCollections().toArray();
      
      for (const collection of collections) {
        await db.collection(collection.name).deleteMany({});
      }
      
      logger.warn('⚠️ MongoDB database cleared');
    } catch (error) {
      logError('Failed to clear MongoDB database', error);
      throw error;
    }
  }
}

// Create singleton instance
const mongoDBConnection = new MongoDBConnection();

// Export connection functions
export const connectMongoDB = () => mongoDBConnection.connect();
export const disconnectMongoDB = () => mongoDBConnection.disconnect();
export const getMongoDBConnection = () => mongoDBConnection.getConnection();
export const mongoDBHealthCheck = () => mongoDBConnection.healthCheck();
export const getMongoDBStats = () => mongoDBConnection.getStats();
export const clearMongoDBDatabase = () => mongoDBConnection.clearDatabase();

// Export mongoose for model creation
export { mongoose };

export default mongoDBConnection;
