import winston from 'winston';
export declare const logger: winston.Logger;
export declare const loggerStream: {
    write: (message: string) => void;
};
export declare const logInfo: (message: string, meta?: any) => void;
export declare const logError: (message: string, error?: Error | any, meta?: any) => void;
export declare const logWarn: (message: string, meta?: any) => void;
export declare const logDebug: (message: string, meta?: any) => void;
export declare const logPerformance: (operation: string, startTime: number, meta?: any) => void;
export declare const logDatabaseOperation: (operation: string, collection: string, duration?: number, meta?: any) => void;
export declare const logApiRequest: (method: string, url: string, statusCode: number, duration: number, meta?: any) => void;
export declare const logAiOperation: (operation: string, model: string, tokens?: number, duration?: number, meta?: any) => void;
export declare const logGraphOperation: (operation: string, nodeCount?: number, relationshipCount?: number, duration?: number, meta?: any) => void;
export declare const logSecurityEvent: (event: string, severity: "low" | "medium" | "high" | "critical", meta?: any) => void;
export declare const logBusinessEvent: (event: string, entityType?: string, entityId?: string, meta?: any) => void;
export default logger;
//# sourceMappingURL=logger.d.ts.map