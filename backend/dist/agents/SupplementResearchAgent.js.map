{"version": 3, "file": "SupplementResearchAgent.js", "sourceRoot": "", "sources": ["../../src/agents/SupplementResearchAgent.ts"], "names": [], "mappings": ";;;AAAA,0CAKuB;AACvB,+BAAkC;AAClC,iEAA8D;AAC9D,2DAAwD;AACxD,2DAAwD;AACxD,4CAAyC;AAQzC,MAAa,uBAAwB,SAAQ,sBAAa;IAChD,eAAe,CAAkB;IACjC,YAAY,CAAe;IAC3B,YAAY,CAAe;IAEnC;QACE,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;QAC7C,IAAI,CAAC,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;QACvC,IAAI,CAAC,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;IACzC,CAAC;IAES,GAAG,CAAC,KAA8B;QAC1C,OAAO,IAAI,iBAAU,CAAY,CAAC,QAAQ,EAAE,EAAE;YAC5C,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,uBAAuB,CACnC,KAA8B,EAC9B,QAAa;QAEb,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QAExC,IAAI,CAAC;YAEH,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,kBAAS,CAAC,WAAW;gBAC3B,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACxB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;YAEH,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,kBAAS,CAAC,kBAAkB;gBAClC,SAAS;aACV,CAAC,CAAC;YAGH,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,kBAAS,CAAC,oBAAoB;gBACpC,SAAS;gBACT,KAAK,EAAE,wCAAwC,KAAK,CAAC,cAAc,IAAI;aACxE,CAAC,CAAC;YAEH,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,kBAAS,CAAC,eAAe;gBAC/B,UAAU,EAAE,YAAY,IAAI,CAAC,GAAG,EAAE,EAAE;gBACpC,YAAY,EAAE,iBAAiB;gBAC/B,eAAe,EAAE,SAAS;aAC3B,CAAC,CAAC;YAGH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,wBAAwB,CACtE,KAAK,CAAC,cAAc,EACpB;gBACE,mBAAmB,EAAE,KAAK,CAAC,mBAAmB,IAAI,KAAK;gBACvD,aAAa,EAAE,KAAK,CAAC,aAAa,IAAI,OAAO;aAC9C,CACF,CAAC;YAEF,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,kBAAS,CAAC,aAAa;gBAC7B,UAAU,EAAE,YAAY,IAAI,CAAC,GAAG,EAAE,EAAE;gBACpC,YAAY,EAAE,iBAAiB;gBAC/B,eAAe,EAAE,SAAS;aAC3B,CAAC,CAAC;YAEH,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,kBAAS,CAAC,oBAAoB;gBACpC,SAAS;gBACT,KAAK,EAAE,cAAc,YAAY,CAAC,eAAe,EAAE,MAAM,IAAI,CAAC,qBAAqB;aACpF,CAAC,CAAC;YAGH,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,kBAAS,CAAC,eAAe;gBAC/B,UAAU,EAAE,kBAAkB,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC1C,YAAY,EAAE,gBAAgB;gBAC9B,eAAe,EAAE,SAAS;aAC3B,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAC5D,KAAK,CAAC,cAAc,EACpB,YAAY,CACb,CAAC;YAEF,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,kBAAS,CAAC,aAAa;gBAC7B,UAAU,EAAE,kBAAkB,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC1C,YAAY,EAAE,gBAAgB;gBAC9B,eAAe,EAAE,SAAS;aAC3B,CAAC,CAAC;YAEH,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,kBAAS,CAAC,oBAAoB;gBACpC,SAAS;gBACT,KAAK,EAAE,uCAAuC,QAAQ,CAAC,aAAa,CAAC,MAAM,mBAAmB;aAC/F,CAAC,CAAC;YAGH,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,kBAAS,CAAC,eAAe;gBAC/B,UAAU,EAAE,gBAAgB,IAAI,CAAC,GAAG,EAAE,EAAE;gBACxC,YAAY,EAAE,wBAAwB;gBACtC,eAAe,EAAE,SAAS;aAC3B,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,yBAAyB,CACnE,KAAK,CAAC,cAAc,EACpB,QAAQ,EACR,YAAY,CACb,CAAC;YAEF,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,kBAAS,CAAC,aAAa;gBAC7B,UAAU,EAAE,gBAAgB,IAAI,CAAC,GAAG,EAAE,EAAE;gBACxC,YAAY,EAAE,wBAAwB;gBACtC,eAAe,EAAE,SAAS;aAC3B,CAAC,CAAC;YAGH,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,kBAAS,CAAC,WAAW;gBAC3B,KAAK,EAAE;oBACL,UAAU,EAAE;wBACV,IAAI,EAAE,KAAK,CAAC,cAAc;wBAC1B,KAAK,EAAE,WAAW,CAAC,YAAY,IAAI,CAAC;wBACpC,KAAK,EAAE,WAAW,CAAC,oBAAoB,IAAI,CAAC;wBAC5C,aAAa,EAAE,QAAQ,CAAC,aAAa;wBACrC,UAAU,EAAE,QAAQ,CAAC,UAAU;qBAChC;iBACF;aACF,CAAC,CAAC;YAEH,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,kBAAS,CAAC,oBAAoB;gBACpC,SAAS;gBACT,KAAK,EAAE,mCAAmC,WAAW,CAAC,YAAY,IAAI,CAAC,cAAc,WAAW,CAAC,oBAAoB,IAAI,CAAC,gBAAgB;aAC3I,CAAC,CAAC;YAGH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,CACvD,KAAK,CAAC,cAAc,EACpB,QAAQ,EACR,WAAW,CACZ,CAAC;YAEF,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,kBAAS,CAAC,oBAAoB;gBACpC,SAAS;gBACT,KAAK,EAAE,2BAA2B,QAAQ,CAAC,OAAO,MAAM;aACzD,CAAC,CAAC;YAEH,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;gBAC9C,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,kBAAS,CAAC,oBAAoB;oBACpC,SAAS;oBACT,KAAK,EAAE,GAAG,KAAK,GAAG,CAAC,KAAK,OAAO,IAAI;iBACpC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjC,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,kBAAS,CAAC,oBAAoB;oBACpC,SAAS;oBACT,KAAK,EAAE,sCAAsC;iBAC9C,CAAC,CAAC;gBAEH,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;oBAC3C,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,kBAAS,CAAC,oBAAoB;wBACpC,SAAS;wBACT,KAAK,EAAE,GAAG,KAAK,GAAG,CAAC,KAAK,OAAO,IAAI;qBACpC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC;YAED,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,kBAAS,CAAC,oBAAoB;gBACpC,SAAS;gBACT,KAAK,EAAE,qGAAqG,KAAK,CAAC,cAAc,GAAG;aACpI,CAAC,CAAC;YAEH,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,kBAAS,CAAC,gBAAgB;gBAChC,SAAS;aACV,CAAC,CAAC;YAEH,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,kBAAS,CAAC,YAAY;gBAC5B,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACxB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;YAEH,QAAQ,CAAC,QAAQ,EAAE,CAAC;QAEtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAE9D,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,kBAAS,CAAC,SAAS;gBACzB,OAAO,EAAE,oBAAoB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;aACxF,CAAC,CAAC;YAEH,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACxB,CAAC;IACH,CAAC;CACF;AA/MD,0DA+MC"}