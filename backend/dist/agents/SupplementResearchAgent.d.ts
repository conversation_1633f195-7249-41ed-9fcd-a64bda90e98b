import { AbstractAgent, RunAgentInput, BaseEvent } from '@ag-ui/client';
import { Observable } from 'rxjs';
export interface SupplementResearchInput extends RunAgentInput {
    supplementName: string;
    researchDepth?: 'basic' | 'comprehensive';
    includeInteractions?: boolean;
}
export declare class SupplementResearchAgent extends AbstractAgent {
    private researchService;
    private gemmaService;
    private graphService;
    constructor();
    protected run(input: SupplementResearchInput): Observable<BaseEvent>;
    private executeResearchPipeline;
}
//# sourceMappingURL=SupplementResearchAgent.d.ts.map