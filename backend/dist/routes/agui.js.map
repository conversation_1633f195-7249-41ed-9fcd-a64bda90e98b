{"version": 3, "file": "agui.js", "sourceRoot": "", "sources": ["../../src/routes/agui.ts"], "names": [], "mappings": ";;;;;AAeA,0DAkDC;AAjED,sDAA8B;AAC9B,2BAAgD;AAChD,+EAA4E;AAC5E,4CAAyC;AACzC,+BAAoC;AAEpC,MAAM,MAAM,GAAG,iBAAO,CAAC,MAAM,EAAE,CAAC;AAGhC,MAAM,YAAY,GAAG,IAAI,GAAG,EAAmC,CAAC;AAChE,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAqB,CAAC;AAKvD,SAAgB,uBAAuB,CAAC,MAAW;IACjD,MAAM,GAAG,GAAG,IAAI,oBAAe,CAAC;QAC9B,MAAM;QACN,IAAI,EAAE,UAAU;KACjB,CAAC,CAAC;IAEH,GAAG,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,EAAa,EAAE,OAAO,EAAE,EAAE;QAC9C,MAAM,YAAY,GAAG,IAAA,SAAM,GAAE,CAAC;QAC9B,iBAAiB,CAAC,GAAG,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QAExC,eAAM,CAAC,IAAI,CAAC,2CAA2C,YAAY,EAAE,CAAC,CAAC;QAEvE,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,IAAY,EAAE,EAAE;YACtC,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC5C,MAAM,iBAAiB,CAAC,YAAY,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;YACrD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;gBACrD,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;oBACrB,IAAI,EAAE,OAAO;oBACb,OAAO,EAAE,wBAAwB;iBAClC,CAAC,CAAC,CAAC;YACN,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YAClB,eAAM,CAAC,IAAI,CAAC,sCAAsC,YAAY,EAAE,CAAC,CAAC;YAClE,iBAAiB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAGvC,KAAK,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;gBACtD,IAAI,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;oBACrC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YACvB,eAAM,CAAC,KAAK,CAAC,6BAA6B,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAGH,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;YACrB,IAAI,EAAE,wBAAwB;YAC9B,YAAY;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC;IAEH,OAAO,GAAG,CAAC;AACb,CAAC;AAKD,KAAK,UAAU,iBAAiB,CAC9B,YAAoB,EACpB,OAAY,EACZ,EAAa;IAEb,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;IAElC,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,2BAA2B;YAC9B,MAAM,wBAAwB,CAAC,YAAY,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;YAC1D,MAAM;QAER,KAAK,wBAAwB;YAC3B,MAAM,0BAA0B,CAAC,YAAY,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;YAC5D,MAAM;QAER,KAAK,iBAAiB;YACpB,MAAM,mBAAmB,CAAC,YAAY,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;YACrD,MAAM;QAER,KAAK,MAAM;YACT,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC;YAC/E,MAAM;QAER;YACE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;gBACrB,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,yBAAyB,IAAI,EAAE;aACzC,CAAC,CAAC,CAAC;IACR,CAAC;AACH,CAAC;AAKD,KAAK,UAAU,wBAAwB,CACrC,YAAoB,EACpB,OAAY,EACZ,EAAa;IAEb,IAAI,CAAC;QACH,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,mBAAmB,EAAE,GAAG,OAAO,CAAC;QAEvE,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;gBACrB,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,6BAA6B;aACvC,CAAC,CAAC,CAAC;YACJ,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG,GAAG,YAAY,IAAI,IAAA,SAAM,GAAE,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,IAAI,iDAAuB,EAAE,CAAC;QAC5C,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAEjC,MAAM,KAAK,GAAG,IAAA,SAAM,GAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAA,SAAM,GAAE,CAAC;QAG1B,MAAM,aAAa,GAAG;YACpB,cAAc;YACd,aAAa,EAAE,aAAa,IAAI,OAAO;YACvC,mBAAmB,EAAE,mBAAmB,IAAI,KAAK;YACjD,KAAK;YACL,QAAQ;YACR,OAAO;SACR,CAAC;QAGF,MAAM,YAAY,GAAG,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC;YACtD,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE;gBACd,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;oBACrB,IAAI,EAAE,aAAa;oBACnB,OAAO;oBACP,KAAK;iBACN,CAAC,CAAC,CAAC;YACN,CAAC;YACD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,eAAM,CAAC,KAAK,CAAC,mBAAmB,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;gBACnD,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;oBACrB,IAAI,EAAE,aAAa;oBACnB,OAAO;oBACP,KAAK,EAAE,KAAK,CAAC,OAAO;iBACrB,CAAC,CAAC,CAAC;gBACJ,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC/B,CAAC;YACD,QAAQ,EAAE,GAAG,EAAE;gBACb,eAAM,CAAC,IAAI,CAAC,uBAAuB,OAAO,EAAE,CAAC,CAAC;gBAC9C,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;oBACrB,IAAI,EAAE,iBAAiB;oBACvB,OAAO;iBACR,CAAC,CAAC,CAAC;gBACJ,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC/B,CAAC;SACF,CAAC,CAAC;QAGF,KAAa,CAAC,YAAY,GAAG,YAAY,CAAC;IAE7C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC3D,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;YACrB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,qCAAqC;SAC/C,CAAC,CAAC,CAAC;IACN,CAAC;AACH,CAAC;AAKD,KAAK,UAAU,0BAA0B,CACvC,YAAoB,EACpB,OAAY,EACZ,EAAa;IAEb,IAAI,CAAC;QACH,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;QAEnC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;gBACrB,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,mBAAmB;aAC7B,CAAC,CAAC,CAAC;YACJ,OAAO;QACT,CAAC;QAGD,MAAM,KAAK,GAAG,IAAI,iDAAuB,EAAE,CAAC;QAC5C,MAAM,YAAY,GAAI,KAAa,CAAC,YAAY,CAAC;QAEjD,MAAM,QAAQ,GAAG,MAAM,YAAY,CAAC,2BAA2B,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAEhF,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;YACrB,IAAI,EAAE,gBAAgB;YACtB,KAAK;YACL,QAAQ;YACR,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC,CAAC;IAEN,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;QAChE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;YACrB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,yBAAyB;SACnC,CAAC,CAAC,CAAC;IACN,CAAC;AACH,CAAC;AAKD,KAAK,UAAU,mBAAmB,CAChC,YAAoB,EACpB,OAAY,EACZ,EAAa;IAEb,IAAI,CAAC;QACH,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;QAGnD,MAAM,KAAK,GAAG,IAAI,iDAAuB,EAAE,CAAC;QAC5C,MAAM,YAAY,GAAI,KAAa,CAAC,YAAY,CAAC;QAEjD,MAAM,SAAS,GAAG,MAAM,YAAY,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAEvE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;YACrB,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,SAAS;YACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC,CAAC;IAEN,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QAClD,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;YACrB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,2BAA2B;SACrC,CAAC,CAAC,CAAC;IACN,CAAC;AACH,CAAC;AAOD,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;IACjC,GAAG,CAAC,IAAI,CAAC;QACP,MAAM,EAAE;YACN;gBACE,EAAE,EAAE,qBAAqB;gBACzB,IAAI,EAAE,2BAA2B;gBACjC,WAAW,EAAE,oDAAoD;gBACjE,YAAY,EAAE;oBACZ,qBAAqB;oBACrB,0BAA0B;oBAC1B,0BAA0B;iBAC3B;aACF;SACF;KACF,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAGH,MAAM,CAAC,GAAG,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;IACjD,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAC/B,MAAM,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAExC,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,iBAAiB,EAAE,CAAC,CAAC;IAC5D,CAAC;IAED,GAAG,CAAC,IAAI,CAAC;QACP,OAAO;QACP,MAAM,EAAE,QAAQ;QAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAGH,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACvC,IAAI,CAAC;QAEH,MAAM,KAAK,GAAG,IAAI,iDAAuB,EAAE,CAAC;QAC5C,MAAM,YAAY,GAAI,KAAa,CAAC,YAAY,CAAC;QACjD,MAAM,YAAY,GAAI,KAAa,CAAC,YAAY,CAAC;QAEjD,MAAM,CAAC,WAAW,EAAE,WAAW,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACnD,YAAY,CAAC,WAAW,EAAE;YAC1B,YAAY,CAAC,WAAW,EAAE;SAC3B,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC;YACP,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE;gBACR,KAAK,EAAE,WAAW;gBAClB,KAAK,EAAE,WAAW;gBAClB,SAAS,EAAE,IAAI;aAChB;YACD,iBAAiB,EAAE,iBAAiB,CAAC,IAAI;YACzC,YAAY,EAAE,YAAY,CAAC,IAAI;YAC/B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QAClD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,MAAM,EAAE,WAAW;YACnB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;SAChE,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,kBAAe,MAAM,CAAC"}