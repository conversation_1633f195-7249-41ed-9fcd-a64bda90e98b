"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.graphRoutes = void 0;
const express_1 = require("express");
const errorHandler_1 = require("@/middleware/errorHandler");
const express_validator_1 = require("express-validator");
const GraphService_1 = require("@/services/GraphService");
const router = (0, express_1.Router)();
exports.graphRoutes = router;
const graphService = new GraphService_1.GraphService();
const validateRequest = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError(`Validation failed: ${errors.array().map(e => e.msg).join(', ')}`);
    }
    next();
};
router.get('/', [
    (0, express_validator_1.query)('nodeTypes').optional().isString().withMessage('nodeTypes must be a string'),
    (0, express_validator_1.query)('relationshipTypes').optional().isString().withMessage('relationshipTypes must be a string'),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 10000 }).withMessage('limit must be between 1 and 10000'),
    (0, express_validator_1.query)('search').optional().isString().withMessage('search must be a string'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { nodeTypes, relationshipTypes, limit = 1000, search, } = req.query;
    const filters = {
        nodeTypes: nodeTypes ? nodeTypes.split(',') : undefined,
        relationshipTypes: relationshipTypes ? relationshipTypes.split(',') : undefined,
        limit: parseInt(limit),
        search: search,
    };
    const graphData = await graphService.getGraphData(filters);
    res.status(200).json({
        success: true,
        data: graphData,
        timestamp: new Date().toISOString(),
    });
}));
router.get('/nodes/:id', (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { id } = req.params;
    const node = await graphService.getNodeById(id);
    res.status(200).json({
        success: true,
        data: node,
        timestamp: new Date().toISOString(),
    });
}));
router.get('/nodes/:id/relationships', [
    (0, express_validator_1.query)('direction').optional().isIn(['incoming', 'outgoing', 'both']).withMessage('direction must be incoming, outgoing, or both'),
    (0, express_validator_1.query)('types').optional().isString().withMessage('types must be a string'),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 1000 }).withMessage('limit must be between 1 and 1000'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { id } = req.params;
    const { direction = 'both', types, limit = 100, } = req.query;
    const filters = {
        direction: direction,
        types: types ? types.split(',') : undefined,
        limit: parseInt(limit),
    };
    const relationships = await graphService.getNodeRelationships(id, filters);
    res.status(200).json({
        success: true,
        data: relationships,
        timestamp: new Date().toISOString(),
    });
}));
router.get('/search', [
    (0, express_validator_1.query)('q').notEmpty().withMessage('Search query is required'),
    (0, express_validator_1.query)('nodeTypes').optional().isString().withMessage('nodeTypes must be a string'),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }).withMessage('limit must be between 1 and 100'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { q: query, nodeTypes, limit = 20, } = req.query;
    const filters = {
        nodeTypes: nodeTypes ? nodeTypes.split(',') : undefined,
        limit: parseInt(limit),
    };
    const results = await graphService.searchNodes(query, filters);
    res.status(200).json({
        success: true,
        data: results,
        timestamp: new Date().toISOString(),
    });
}));
router.post('/nodes', [
    (0, express_validator_1.body)('type').notEmpty().withMessage('Node type is required'),
    (0, express_validator_1.body)('properties').isObject().withMessage('Properties must be an object'),
    (0, express_validator_1.body)('properties.name').notEmpty().withMessage('Node name is required'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { type, properties } = req.body;
    const node = await graphService.createNode(type, properties);
    res.status(201).json({
        success: true,
        data: node,
        message: 'Node created successfully',
        timestamp: new Date().toISOString(),
    });
}));
router.put('/nodes/:id', [
    (0, express_validator_1.body)('properties').isObject().withMessage('Properties must be an object'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { id } = req.params;
    const { properties } = req.body;
    const node = await graphService.updateNode(id, properties);
    res.status(200).json({
        success: true,
        data: node,
        message: 'Node updated successfully',
        timestamp: new Date().toISOString(),
    });
}));
router.delete('/nodes/:id', (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { id } = req.params;
    await graphService.deleteNode(id);
    res.status(200).json({
        success: true,
        message: 'Node deleted successfully',
        timestamp: new Date().toISOString(),
    });
}));
router.post('/relationships', [
    (0, express_validator_1.body)('fromId').notEmpty().withMessage('From node ID is required'),
    (0, express_validator_1.body)('toId').notEmpty().withMessage('To node ID is required'),
    (0, express_validator_1.body)('type').notEmpty().withMessage('Relationship type is required'),
    (0, express_validator_1.body)('properties').optional().isObject().withMessage('Properties must be an object'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { fromId, toId, type, properties = {} } = req.body;
    const relationship = await graphService.createRelationship(fromId, toId, type, properties);
    res.status(201).json({
        success: true,
        data: relationship,
        message: 'Relationship created successfully',
        timestamp: new Date().toISOString(),
    });
}));
router.post('/extract', [
    (0, express_validator_1.body)('text').notEmpty().withMessage('Text is required'),
    (0, express_validator_1.body)('source').optional().isString().withMessage('Source must be a string'),
    (0, express_validator_1.body)('extractionType').optional().isIn(['supplement', 'ingredient', 'study', 'general']).withMessage('Invalid extraction type'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { text, source, extractionType = 'general' } = req.body;
    const extractedData = await graphService.extractKnowledgeFromText(text, {
        source,
        extractionType,
    });
    res.status(200).json({
        success: true,
        data: extractedData,
        message: 'Knowledge extracted successfully',
        timestamp: new Date().toISOString(),
    });
}));
router.post('/expand', [
    (0, express_validator_1.body)('nodeId').notEmpty().withMessage('Node ID is required'),
    (0, express_validator_1.body)('expansionType').optional().isIn(['related', 'similar', 'interactions', 'studies']).withMessage('Invalid expansion type'),
    (0, express_validator_1.body)('limit').optional().isInt({ min: 1, max: 100 }).withMessage('limit must be between 1 and 100'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { nodeId, expansionType = 'related', limit = 10 } = req.body;
    const expandedData = await graphService.expandGraph(nodeId, {
        expansionType,
        limit,
    });
    res.status(200).json({
        success: true,
        data: expandedData,
        message: 'Graph expanded successfully',
        timestamp: new Date().toISOString(),
    });
}));
router.get('/stats', (0, errorHandler_1.catchAsync)(async (req, res) => {
    const stats = await graphService.getGraphStats();
    res.status(200).json({
        success: true,
        data: stats,
        timestamp: new Date().toISOString(),
    });
}));
router.delete('/cleanup', [
    (0, express_validator_1.query)('dryRun').optional().isBoolean().withMessage('dryRun must be a boolean'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { dryRun = true } = req.query;
    const cleanupResults = await graphService.cleanupGraph(dryRun === 'true');
    res.status(200).json({
        success: true,
        data: cleanupResults,
        message: dryRun === 'true' ? 'Cleanup analysis completed' : 'Graph cleanup completed',
        timestamp: new Date().toISOString(),
    });
}));
//# sourceMappingURL=graph.js.map