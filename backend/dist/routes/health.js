"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.healthRoutes = void 0;
const express_1 = require("express");
const errorHandler_1 = require("@/middleware/errorHandler");
const neo4j_1 = require("@/config/neo4j");
const weaviate_1 = require("@/config/weaviate");
const redis_1 = require("@/config/redis");
const mongodb_1 = require("@/config/mongodb");
const environment_1 = require("@/config/environment");
const router = (0, express_1.Router)();
exports.healthRoutes = router;
router.get('/', (0, errorHandler_1.catchAsync)(async (req, res) => {
    res.status(200).json({
        success: true,
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: environment_1.config.nodeEnv,
        version: '1.0.0',
    });
}));
router.get('/detailed', (0, errorHandler_1.catchAsync)(async (req, res) => {
    const healthChecks = await Promise.allSettled([
        (0, neo4j_1.neo4jHealthCheck)(),
        (0, weaviate_1.weaviateHealthCheck)(),
        (0, redis_1.redisHealthCheck)(),
        (0, mongodb_1.mongoDBHealthCheck)(),
    ]);
    const [neo4jHealth, weaviateHealth, redisHealth, mongoDBHealth] = healthChecks;
    const services = {
        neo4j: {
            status: neo4jHealth.status === 'fulfilled' && neo4jHealth.value ? 'healthy' : 'unhealthy',
            error: neo4jHealth.status === 'rejected' ? neo4jHealth.reason?.message : null,
        },
        weaviate: {
            status: weaviateHealth.status === 'fulfilled' && weaviateHealth.value ? 'healthy' : 'unhealthy',
            error: weaviateHealth.status === 'rejected' ? weaviateHealth.reason?.message : null,
        },
        redis: {
            status: redisHealth.status === 'fulfilled' && redisHealth.value ? 'healthy' : 'unhealthy',
            error: redisHealth.status === 'rejected' ? redisHealth.reason?.message : null,
        },
        mongodb: {
            status: mongoDBHealth.status === 'fulfilled' && mongoDBHealth.value ? 'healthy' : 'unhealthy',
            error: mongoDBHealth.status === 'rejected' ? mongoDBHealth.reason?.message : null,
        },
    };
    const allHealthy = Object.values(services).every(service => service.status === 'healthy');
    res.status(allHealthy ? 200 : 503).json({
        success: allHealthy,
        status: allHealthy ? 'OK' : 'DEGRADED',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: environment_1.config.nodeEnv,
        version: '1.0.0',
        services,
    });
}));
router.get('/stats', (0, errorHandler_1.catchAsync)(async (req, res) => {
    const statsPromises = await Promise.allSettled([
        (0, neo4j_1.getNeo4jStats)(),
        (0, weaviate_1.getWeaviateStats)(),
        (0, redis_1.getRedisStats)(),
        (0, mongodb_1.getMongoDBStats)(),
    ]);
    const [neo4jStats, weaviateStats, redisStats, mongoDBStats] = statsPromises;
    const stats = {
        neo4j: neo4jStats.status === 'fulfilled' ? neo4jStats.value : { error: neo4jStats.reason?.message },
        weaviate: weaviateStats.status === 'fulfilled' ? weaviateStats.value : { error: weaviateStats.reason?.message },
        redis: redisStats.status === 'fulfilled' ? redisStats.value : { error: redisStats.reason?.message },
        mongodb: mongoDBStats.status === 'fulfilled' ? mongoDBStats.value : { error: mongoDBStats.reason?.message },
    };
    res.status(200).json({
        success: true,
        timestamp: new Date().toISOString(),
        stats,
    });
}));
router.get('/system', (0, errorHandler_1.catchAsync)(async (req, res) => {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    res.status(200).json({
        success: true,
        timestamp: new Date().toISOString(),
        system: {
            nodeVersion: process.version,
            platform: process.platform,
            arch: process.arch,
            uptime: process.uptime(),
            memory: {
                rss: Math.round(memoryUsage.rss / 1024 / 1024),
                heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
                heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
                external: Math.round(memoryUsage.external / 1024 / 1024),
            },
            cpu: {
                user: cpuUsage.user,
                system: cpuUsage.system,
            },
        },
    });
}));
//# sourceMappingURL=health.js.map