export interface ResearchQuery {
    query: string;
    type: 'web_search' | 'academic' | 'news' | 'supplement_specific';
    filters?: {
        timeRange?: 'day' | 'week' | 'month' | 'year';
        domains?: string[];
        excludeDomains?: string[];
        maxResults?: number;
    };
    context?: string;
}
export interface ResearchResult {
    title: string;
    url: string;
    snippet: string;
    content?: string;
    publishedDate?: string;
    domain: string;
    relevanceScore: number;
    medicalRelevance?: number;
    extractedEntities?: string[];
}
export interface ResearchResponse {
    query: string;
    results: ResearchResult[];
    totalResults: number;
    searchTime: number;
    suggestions?: string[];
    relatedQueries?: string[];
}
export declare class ResearchService {
    private braveClient;
    private tavilyClient;
    constructor();
    searchWeb(query: ResearchQuery): Promise<ResearchResponse>;
    searchTavily(query: ResearchQuery): Promise<ResearchResponse>;
    searchSupplements(query: ResearchQuery): Promise<ResearchResponse>;
    comprehensiveSearch(query: ResearchQuery): Promise<ResearchResponse>;
    private enhanceQuery;
    private enhanceSupplementQuery;
    private processBraveResults;
    private processTavilyResults;
    private extractDomain;
    private calculateRelevance;
    private calculateMedicalRelevance;
    private deduplicateResults;
    private generateRelatedQueries;
    gatherSupplementResearch(supplementName: string, options?: {
        includeInteractions?: boolean;
        researchDepth?: 'basic' | 'comprehensive';
    }): Promise<any>;
    healthCheck(): Promise<{
        brave: boolean;
        tavily: boolean;
    }>;
}
//# sourceMappingURL=ResearchService.d.ts.map