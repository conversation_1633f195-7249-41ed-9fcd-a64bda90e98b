export interface CrawlRequest {
    url: string;
    type: 'single_page' | 'recursive' | 'sitemap' | 'llms_txt';
    options?: {
        maxDepth?: number;
        maxPages?: number;
        includePatterns?: string[];
        excludePatterns?: string[];
        extractImages?: boolean;
        chunkSize?: number;
        followExternalLinks?: boolean;
    };
}
export interface CrawlResult {
    url: string;
    title: string;
    content: string;
    chunks: ContentChunk[];
    metadata: PageMetadata;
    extractedData?: ExtractedData;
    crawlTime: number;
}
export interface ContentChunk {
    id: string;
    content: string;
    headers: string[];
    wordCount: number;
    charCount: number;
    chunkIndex: number;
}
export interface PageMetadata {
    title: string;
    description?: string;
    keywords?: string[];
    author?: string;
    publishedDate?: string;
    lastModified?: string;
    language?: string;
    domain: string;
}
export interface ExtractedData {
    supplements: string[];
    ingredients: string[];
    dosages: string[];
    benefits: string[];
    sideEffects: string[];
    interactions: string[];
}
export interface CrawlProgress {
    totalPages: number;
    completedPages: number;
    currentUrl: string;
    status: 'running' | 'completed' | 'failed' | 'paused';
    errors: string[];
}
export declare class CrawlService {
    private crawlScriptPath;
    private tempDir;
    private activeCrawls;
    constructor();
    crawlSinglePage(request: CrawlRequest): Promise<CrawlResult>;
    crawlRecursive(request: CrawlRequest): Promise<CrawlResult[]>;
    crawlSitemap(request: CrawlRequest): Promise<CrawlResult[]>;
    crawlMarkdown(request: CrawlRequest): Promise<CrawlResult>;
    getCrawlProgress(crawlId: string): Promise<CrawlProgress>;
    cancelCrawl(crawlId: string): Promise<boolean>;
    private executeCrawl;
    private executeBatchCrawl;
    private processCrawlResult;
    private extractSupplementData;
    private extractKeywords;
    private extractDomain;
    private generateCrawlId;
    private ensureTempDir;
    private cleanupTempFiles;
    healthCheck(): Promise<boolean>;
}
//# sourceMappingURL=CrawlService.d.ts.map