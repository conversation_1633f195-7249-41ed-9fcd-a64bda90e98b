interface QueryOptions {
    maxResults?: number;
    similarityThreshold?: number;
    includeMetadata?: boolean;
    context?: any[];
}
interface SimilarityOptions {
    className?: string;
    limit?: number;
    threshold?: number;
}
interface DocumentData {
    content: string;
    title?: string;
    source?: string;
    metadata?: any;
    className?: string;
}
interface SearchOptions {
    className?: string;
    limit?: number;
    offset?: number;
}
interface AnswerOptions {
    context?: any[];
    useGraph?: boolean;
    maxContextLength?: number;
}
interface ReindexOptions {
    className?: string;
    batchSize?: number;
}
export declare class RAGService {
    private aiService;
    constructor();
    queryKnowledgeBase(question: string, options: QueryOptions): Promise<any>;
    createEmbedding(text: string, metadata?: any, className?: string): Promise<any>;
    findSimilarByText(text: string, options: SimilarityOptions): Promise<any[]>;
    findSimilarByVector(vector: number[], options: SimilarityOptions): Promise<any[]>;
    addDocument(documentData: DocumentData): Promise<any>;
    getDocument(id: string): Promise<any>;
    updateDocument(id: string, updates: Partial<DocumentData>): Promise<any>;
    deleteDocument(id: string): Promise<void>;
    searchDocuments(query: string, options: SearchOptions): Promise<any>;
    listDocuments(options: SearchOptions): Promise<any>;
    generateAnswer(question: string, options: AnswerOptions): Promise<any>;
    getStats(): Promise<any>;
    reindexDocuments(options: ReindexOptions): Promise<any>;
    private reindexClass;
    private invalidateDocumentCache;
    private invalidateCache;
    private hashString;
}
export {};
//# sourceMappingURL=RAGService.d.ts.map