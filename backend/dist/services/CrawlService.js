"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrawlService = void 0;
const child_process_1 = require("child_process");
const fs_1 = require("fs");
const path_1 = __importDefault(require("path"));
const logger_1 = require("../utils/logger");
class CrawlService {
    crawlScriptPath;
    tempDir;
    activeCrawls = new Map();
    constructor() {
        this.crawlScriptPath = path_1.default.join(__dirname, '../../scripts/crawl4ai_wrapper.py');
        this.tempDir = path_1.default.join(__dirname, '../../temp/crawl');
        this.ensureTempDir();
    }
    async crawlSinglePage(request) {
        try {
            const startTime = Date.now();
            const crawlId = this.generateCrawlId();
            const result = await this.executeCrawl(crawlId, {
                ...request,
                type: 'single_page'
            });
            const crawlTime = Date.now() - startTime;
            return {
                ...result,
                crawlTime
            };
        }
        catch (error) {
            logger_1.logger.error('Single page crawl failed:', error);
            throw new Error('Failed to crawl page');
        }
    }
    async crawlRecursive(request) {
        try {
            const crawlId = this.generateCrawlId();
            const results = await this.executeBatchCrawl(crawlId, {
                ...request,
                type: 'recursive'
            });
            return results;
        }
        catch (error) {
            logger_1.logger.error('Recursive crawl failed:', error);
            throw new Error('Failed to crawl website recursively');
        }
    }
    async crawlSitemap(request) {
        try {
            const crawlId = this.generateCrawlId();
            const results = await this.executeBatchCrawl(crawlId, {
                ...request,
                type: 'sitemap'
            });
            return results;
        }
        catch (error) {
            logger_1.logger.error('Sitemap crawl failed:', error);
            throw new Error('Failed to crawl sitemap');
        }
    }
    async crawlMarkdown(request) {
        try {
            const startTime = Date.now();
            const crawlId = this.generateCrawlId();
            const result = await this.executeCrawl(crawlId, {
                ...request,
                type: 'llms_txt'
            });
            const crawlTime = Date.now() - startTime;
            return {
                ...result,
                crawlTime
            };
        }
        catch (error) {
            logger_1.logger.error('Markdown crawl failed:', error);
            throw new Error('Failed to crawl markdown content');
        }
    }
    async getCrawlProgress(crawlId) {
        try {
            const progressFile = path_1.default.join(this.tempDir, `${crawlId}_progress.json`);
            const progressData = await fs_1.promises.readFile(progressFile, 'utf-8');
            return JSON.parse(progressData);
        }
        catch (error) {
            return {
                totalPages: 0,
                completedPages: 0,
                currentUrl: '',
                status: 'failed',
                errors: ['Progress file not found']
            };
        }
    }
    async cancelCrawl(crawlId) {
        try {
            const process = this.activeCrawls.get(crawlId);
            if (process) {
                process.kill('SIGTERM');
                this.activeCrawls.delete(crawlId);
                return true;
            }
            return false;
        }
        catch (error) {
            logger_1.logger.error('Failed to cancel crawl:', error);
            return false;
        }
    }
    async executeCrawl(crawlId, request) {
        return new Promise((resolve, reject) => {
            const outputFile = path_1.default.join(this.tempDir, `${crawlId}_result.json`);
            const args = [
                this.crawlScriptPath,
                '--url', request.url,
                '--type', request.type,
                '--output', outputFile,
                '--crawl-id', crawlId
            ];
            if (request.options?.maxDepth) {
                args.push('--max-depth', request.options.maxDepth.toString());
            }
            if (request.options?.chunkSize) {
                args.push('--chunk-size', request.options.chunkSize.toString());
            }
            if (request.options?.extractImages) {
                args.push('--extract-images');
            }
            const crawlProcess = (0, child_process_1.spawn)('python3', args);
            this.activeCrawls.set(crawlId, crawlProcess);
            let stdout = '';
            let stderr = '';
            crawlProcess.stdout.on('data', (data) => {
                stdout += data.toString();
            });
            crawlProcess.stderr.on('data', (data) => {
                stderr += data.toString();
            });
            crawlProcess.on('close', async (code) => {
                this.activeCrawls.delete(crawlId);
                if (code === 0) {
                    try {
                        const resultData = await fs_1.promises.readFile(outputFile, 'utf-8');
                        const result = JSON.parse(resultData);
                        await this.cleanupTempFiles(crawlId);
                        resolve(this.processCrawlResult(result));
                    }
                    catch (error) {
                        reject(new Error('Failed to parse crawl result'));
                    }
                }
                else {
                    reject(new Error(`Crawl failed with code ${code}: ${stderr}`));
                }
            });
            crawlProcess.on('error', (error) => {
                this.activeCrawls.delete(crawlId);
                reject(error);
            });
        });
    }
    async executeBatchCrawl(crawlId, request) {
        return new Promise((resolve, reject) => {
            const outputFile = path_1.default.join(this.tempDir, `${crawlId}_batch_result.json`);
            const args = [
                this.crawlScriptPath,
                '--url', request.url,
                '--type', request.type,
                '--output', outputFile,
                '--crawl-id', crawlId,
                '--batch-mode'
            ];
            if (request.options?.maxPages) {
                args.push('--max-pages', request.options.maxPages.toString());
            }
            if (request.options?.maxDepth) {
                args.push('--max-depth', request.options.maxDepth.toString());
            }
            const crawlProcess = (0, child_process_1.spawn)('python3', args);
            this.activeCrawls.set(crawlId, crawlProcess);
            let stdout = '';
            let stderr = '';
            crawlProcess.stdout.on('data', (data) => {
                stdout += data.toString();
            });
            crawlProcess.stderr.on('data', (data) => {
                stderr += data.toString();
            });
            crawlProcess.on('close', async (code) => {
                this.activeCrawls.delete(crawlId);
                if (code === 0) {
                    try {
                        const resultData = await fs_1.promises.readFile(outputFile, 'utf-8');
                        const results = JSON.parse(resultData);
                        await this.cleanupTempFiles(crawlId);
                        resolve(results.map((result) => this.processCrawlResult(result)));
                    }
                    catch (error) {
                        reject(new Error('Failed to parse batch crawl results'));
                    }
                }
                else {
                    reject(new Error(`Batch crawl failed with code ${code}: ${stderr}`));
                }
            });
            crawlProcess.on('error', (error) => {
                this.activeCrawls.delete(crawlId);
                reject(error);
            });
        });
    }
    processCrawlResult(rawResult) {
        const extractedData = this.extractSupplementData(rawResult.content || '');
        return {
            url: rawResult.url || '',
            title: rawResult.title || '',
            content: rawResult.content || '',
            chunks: rawResult.chunks || [],
            metadata: {
                title: rawResult.title || '',
                description: rawResult.description,
                keywords: rawResult.keywords,
                author: rawResult.author,
                publishedDate: rawResult.publishedDate,
                lastModified: rawResult.lastModified,
                language: rawResult.language,
                domain: this.extractDomain(rawResult.url || '')
            },
            extractedData,
            crawlTime: 0
        };
    }
    extractSupplementData(content) {
        const text = content.toLowerCase();
        const supplementKeywords = ['vitamin', 'mineral', 'supplement', 'extract', 'powder', 'capsule'];
        const dosageKeywords = ['mg', 'mcg', 'iu', 'gram', 'dose', 'daily'];
        const benefitKeywords = ['benefit', 'improve', 'enhance', 'support', 'boost'];
        const sideEffectKeywords = ['side effect', 'adverse', 'reaction', 'warning', 'caution'];
        return {
            supplements: this.extractKeywords(text, supplementKeywords),
            ingredients: [],
            dosages: this.extractKeywords(text, dosageKeywords),
            benefits: this.extractKeywords(text, benefitKeywords),
            sideEffects: this.extractKeywords(text, sideEffectKeywords),
            interactions: []
        };
    }
    extractKeywords(text, keywords) {
        const found = [];
        keywords.forEach(keyword => {
            if (text.includes(keyword)) {
                found.push(keyword);
            }
        });
        return found;
    }
    extractDomain(url) {
        try {
            return new URL(url).hostname;
        }
        catch {
            return '';
        }
    }
    generateCrawlId() {
        return `crawl_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    async ensureTempDir() {
        try {
            await fs_1.promises.mkdir(this.tempDir, { recursive: true });
        }
        catch (error) {
            logger_1.logger.error('Failed to create temp directory:', error);
        }
    }
    async cleanupTempFiles(crawlId) {
        try {
            const files = await fs_1.promises.readdir(this.tempDir);
            const crawlFiles = files.filter(file => file.startsWith(crawlId));
            await Promise.all(crawlFiles.map(file => fs_1.promises.unlink(path_1.default.join(this.tempDir, file)).catch(() => { })));
        }
        catch (error) {
            logger_1.logger.error('Failed to cleanup temp files:', error);
        }
    }
    async healthCheck() {
        try {
            return new Promise((resolve) => {
                const testProcess = (0, child_process_1.spawn)('python3', ['-c', 'import crawl4ai; print("OK")']);
                testProcess.on('close', (code) => {
                    resolve(code === 0);
                });
                testProcess.on('error', () => {
                    resolve(false);
                });
            });
        }
        catch (error) {
            return false;
        }
    }
}
exports.CrawlService = CrawlService;
//# sourceMappingURL=CrawlService.js.map