export interface MedicalAnalysisRequest {
    text: string;
    analysisType: 'supplement' | 'interaction' | 'side_effects' | 'dosage' | 'contraindications';
    context?: string;
}
export interface MedicalAnalysisResponse {
    analysis: string;
    entities: MedicalEntity[];
    interactions: DrugInteraction[];
    recommendations: string[];
    confidence: number;
    reasoning: string[];
}
export interface MedicalEntity {
    name: string;
    type: 'supplement' | 'ingredient' | 'condition' | 'effect';
    description: string;
    confidence: number;
}
export interface DrugInteraction {
    substance1: string;
    substance2: string;
    interactionType: 'major' | 'moderate' | 'minor';
    description: string;
    severity: number;
}
export declare class MedicalAIService {
    private client;
    private baseURL;
    constructor();
    analyzeSupplement(request: MedicalAnalysisRequest): Promise<MedicalAnalysisResponse>;
    analyzeInteractions(substances: string[]): Promise<DrugInteraction[]>;
    private buildMedicalPrompt;
    private getSystemPrompt;
    private buildInteractionPrompt;
    private getInteractionSystemPrompt;
    private parseAIResponse;
    private parseInteractions;
    private extractEntities;
    private extractRecommendations;
    private extractReasoning;
    healthCheck(): Promise<boolean>;
}
//# sourceMappingURL=MedicalAIService.d.ts.map