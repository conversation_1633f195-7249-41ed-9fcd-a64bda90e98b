"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResearchService = void 0;
const axios_1 = __importDefault(require("axios"));
const logger_1 = require("../utils/logger");
class ResearchService {
    braveClient;
    tavilyClient;
    constructor() {
        this.braveClient = axios_1.default.create({
            baseURL: 'https://api.search.brave.com/res/v1',
            headers: {
                'X-Subscription-Token': process.env['BRAVE_API_KEY'] || '',
                'Accept': 'application/json',
            },
            timeout: 15000,
        });
        this.tavilyClient = axios_1.default.create({
            baseURL: 'https://api.tavily.com',
            headers: {
                'Content-Type': 'application/json',
            },
            timeout: 20000,
        });
    }
    async searchWeb(query) {
        try {
            const startTime = Date.now();
            const params = {
                q: this.enhanceQuery(query),
                count: query.filters?.maxResults || 10,
                offset: 0,
                mkt: 'en-US',
                safesearch: 'moderate',
                freshness: query.filters?.timeRange || undefined,
                text_decorations: false,
                spellcheck: true,
            };
            const response = await this.braveClient.get('/web/search', { params });
            const searchTime = Date.now() - startTime;
            const results = this.processBraveResults(response.data.web?.results || []);
            return {
                query: query.query,
                results,
                totalResults: response.data.web?.total_count || 0,
                searchTime,
                suggestions: response.data.query?.spellcheck_off ? [response.data.query.original] : [],
                relatedQueries: this.generateRelatedQueries(query.query)
            };
        }
        catch (error) {
            logger_1.logger.error('Brave search failed:', error);
            throw new Error('Web search failed');
        }
    }
    async searchTavily(query) {
        try {
            const startTime = Date.now();
            const requestBody = {
                api_key: process.env['TAVILY_API_KEY'],
                query: this.enhanceQuery(query),
                search_depth: query.type === 'academic' ? 'advanced' : 'basic',
                include_answer: true,
                include_raw_content: true,
                max_results: query.filters?.maxResults || 10,
                include_domains: query.filters?.domains || [],
                exclude_domains: query.filters?.excludeDomains || [],
                include_images: false,
                include_image_descriptions: false,
            };
            const response = await this.tavilyClient.post('/search', requestBody);
            const searchTime = Date.now() - startTime;
            const results = this.processTavilyResults(response.data.results || []);
            return {
                query: query.query,
                results,
                totalResults: results.length,
                searchTime,
                suggestions: [],
                relatedQueries: this.generateRelatedQueries(query.query)
            };
        }
        catch (error) {
            logger_1.logger.error('Tavily search failed:', error);
            throw new Error('Advanced search failed');
        }
    }
    async searchSupplements(query) {
        try {
            const supplementQuery = {
                ...query,
                query: this.enhanceSupplementQuery(query.query),
                filters: {
                    ...query.filters,
                    domains: [
                        'pubmed.ncbi.nlm.nih.gov',
                        'examine.com',
                        'consumerlab.com',
                        'ods.od.nih.gov',
                        'nccih.nih.gov',
                        'webmd.com',
                        'mayoclinic.org',
                        'healthline.com',
                        ...(query.filters?.domains || [])
                    ]
                }
            };
            return await this.searchTavily(supplementQuery);
        }
        catch (error) {
            logger_1.logger.error('Supplement search failed:', error);
            throw new Error('Supplement research failed');
        }
    }
    async comprehensiveSearch(query) {
        try {
            const [braveResults, tavilyResults] = await Promise.allSettled([
                this.searchWeb(query),
                this.searchTavily(query)
            ]);
            const combinedResults = [];
            if (braveResults.status === 'fulfilled') {
                combinedResults.push(...braveResults.value.results);
            }
            if (tavilyResults.status === 'fulfilled') {
                combinedResults.push(...tavilyResults.value.results);
            }
            const uniqueResults = this.deduplicateResults(combinedResults);
            const sortedResults = uniqueResults.sort((a, b) => b.relevanceScore - a.relevanceScore);
            return {
                query: query.query,
                results: sortedResults.slice(0, query.filters?.maxResults || 20),
                totalResults: sortedResults.length,
                searchTime: 0,
                suggestions: [],
                relatedQueries: this.generateRelatedQueries(query.query)
            };
        }
        catch (error) {
            logger_1.logger.error('Comprehensive search failed:', error);
            throw new Error('Research failed');
        }
    }
    enhanceQuery(query) {
        let enhanced = query.query;
        if (query.type === 'supplement_specific') {
            enhanced += ' supplement benefits side effects dosage clinical studies';
        }
        else if (query.type === 'academic') {
            enhanced += ' research study clinical trial pubmed';
        }
        else if (query.type === 'news') {
            enhanced += ' latest news recent developments';
        }
        return enhanced;
    }
    enhanceSupplementQuery(query) {
        const supplementKeywords = [
            'supplement',
            'benefits',
            'side effects',
            'dosage',
            'clinical studies',
            'safety',
            'interactions',
            'efficacy',
            'research'
        ];
        return `${query} ${supplementKeywords.join(' ')}`;
    }
    processBraveResults(results) {
        return results.map(result => ({
            title: result.title || '',
            url: result.url || '',
            snippet: result.description || '',
            publishedDate: result.age || undefined,
            domain: this.extractDomain(result.url || ''),
            relevanceScore: this.calculateRelevance(result),
            medicalRelevance: this.calculateMedicalRelevance(result)
        }));
    }
    processTavilyResults(results) {
        return results.map(result => ({
            title: result.title || '',
            url: result.url || '',
            snippet: result.content || '',
            content: result.raw_content || undefined,
            publishedDate: result.published_date || undefined,
            domain: this.extractDomain(result.url || ''),
            relevanceScore: result.score || 0.5,
            medicalRelevance: this.calculateMedicalRelevance(result)
        }));
    }
    extractDomain(url) {
        try {
            return new URL(url).hostname;
        }
        catch {
            return '';
        }
    }
    calculateRelevance(result) {
        let score = 0.5;
        const medicalDomains = ['pubmed', 'nih.gov', 'mayoclinic', 'webmd', 'examine.com'];
        if (medicalDomains.some(domain => result.url?.includes(domain))) {
            score += 0.3;
        }
        if (result.age && result.age.includes('day')) {
            score += 0.1;
        }
        return Math.min(score, 1.0);
    }
    calculateMedicalRelevance(result) {
        const medicalKeywords = [
            'clinical', 'study', 'research', 'trial', 'supplement', 'vitamin',
            'mineral', 'dosage', 'side effects', 'benefits', 'safety'
        ];
        const text = `${result.title} ${result.description || result.content || ''}`.toLowerCase();
        const matches = medicalKeywords.filter(keyword => text.includes(keyword));
        return matches.length / medicalKeywords.length;
    }
    deduplicateResults(results) {
        const seen = new Set();
        return results.filter(result => {
            const key = result.url;
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }
    generateRelatedQueries(query) {
        const baseQuery = query.toLowerCase();
        const related = [
            `${baseQuery} benefits`,
            `${baseQuery} side effects`,
            `${baseQuery} dosage`,
            `${baseQuery} interactions`,
            `${baseQuery} clinical studies`
        ];
        return related.filter(q => q !== baseQuery);
    }
    async gatherSupplementResearch(supplementName, options = {}) {
        try {
            const query = {
                query: supplementName,
                type: 'supplement_specific',
                filters: {
                    maxResults: options.researchDepth === 'comprehensive' ? 20 : 10,
                    timeRange: 'year'
                }
            };
            const researchResults = await this.searchSupplements(query);
            let interactionData = null;
            if (options.includeInteractions) {
                const interactionQuery = {
                    query: `${supplementName} drug interactions contraindications`,
                    type: 'supplement_specific',
                    filters: {
                        maxResults: 10,
                        domains: ['drugs.com', 'webmd.com', 'mayoclinic.org', 'nih.gov']
                    }
                };
                const interactionResults = await this.searchSupplements(interactionQuery);
                interactionData = interactionResults.results;
            }
            return {
                supplementName,
                researchResults: researchResults.results,
                totalResults: researchResults.totalResults,
                searchTime: researchResults.searchTime,
                interactions: interactionData,
                relatedQueries: researchResults.relatedQueries,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to gather research for ${supplementName}:`, error);
            throw new Error(`Research gathering failed for ${supplementName}`);
        }
    }
    async healthCheck() {
        const checks = await Promise.allSettled([
            this.braveClient.get('/web/search?q=test&count=1'),
            this.tavilyClient.post('/search', {
                api_key: process.env['TAVILY_API_KEY'],
                query: 'test',
                max_results: 1
            })
        ]);
        return {
            brave: checks[0].status === 'fulfilled',
            tavily: checks[1].status === 'fulfilled'
        };
    }
}
exports.ResearchService = ResearchService;
//# sourceMappingURL=ResearchService.js.map