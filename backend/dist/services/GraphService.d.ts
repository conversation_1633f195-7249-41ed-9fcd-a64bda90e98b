interface GraphFilters {
    nodeTypes?: string[];
    relationshipTypes?: string[];
    limit?: number;
    search?: string;
}
interface RelationshipFilters {
    direction?: 'incoming' | 'outgoing' | 'both';
    types?: string[];
    limit?: number;
}
interface SearchFilters {
    nodeTypes?: string[];
    limit?: number;
}
interface ExtractionOptions {
    source?: string;
    extractionType?: 'supplement' | 'ingredient' | 'study' | 'general';
}
interface ExpansionOptions {
    expansionType?: 'related' | 'similar' | 'interactions' | 'studies';
    limit?: number;
}
export declare class GraphService {
    private aiService;
    constructor();
    getGraphData(filters: GraphFilters): Promise<any>;
    getNodeById(id: string): Promise<any>;
    getNodeRelationships(id: string, filters: RelationshipFilters): Promise<any>;
    searchNodes(searchQuery: string, filters: SearchFilters): Promise<any>;
    createNode(type: string, properties: any): Promise<any>;
    updateNode(id: string, properties: any): Promise<any>;
    deleteNode(id: string): Promise<void>;
    createRelationship(fromId: string, toId: string, type: string, properties?: any): Promise<any>;
    extractKnowledgeFromText(text: string, options: ExtractionOptions): Promise<any>;
    expandGraph(nodeId: string, options: ExpansionOptions): Promise<any>;
    getGraphStats(): Promise<any>;
    cleanupGraph(dryRun?: boolean): Promise<any>;
    updateSupplementKnowledge(supplementName: string, analysisData: any, researchData: any): Promise<any>;
    private getEntityTypesForExtraction;
    private invalidateGraphCaches;
}
export {};
//# sourceMappingURL=GraphService.d.ts.map