{"version": 3, "file": "MedicalAIService.d.ts", "sourceRoot": "", "sources": ["../../src/services/MedicalAIService.ts"], "names": [], "mappings": "AAGA,MAAM,WAAW,sBAAsB;IACrC,IAAI,EAAE,MAAM,CAAC;IACb,YAAY,EAAE,YAAY,GAAG,aAAa,GAAG,cAAc,GAAG,QAAQ,GAAG,mBAAmB,CAAC;IAC7F,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED,MAAM,WAAW,uBAAuB;IACtC,QAAQ,EAAE,MAAM,CAAC;IACjB,QAAQ,EAAE,aAAa,EAAE,CAAC;IAC1B,YAAY,EAAE,eAAe,EAAE,CAAC;IAChC,eAAe,EAAE,MAAM,EAAE,CAAC;IAC1B,UAAU,EAAE,MAAM,CAAC;IACnB,SAAS,EAAE,MAAM,EAAE,CAAC;CACrB;AAED,MAAM,WAAW,aAAa;IAC5B,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,YAAY,GAAG,YAAY,GAAG,WAAW,GAAG,QAAQ,CAAC;IAC3D,WAAW,EAAE,MAAM,CAAC;IACpB,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,eAAe;IAC9B,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;IACnB,eAAe,EAAE,OAAO,GAAG,UAAU,GAAG,OAAO,CAAC;IAChD,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,EAAE,MAAM,CAAC;CAClB;AAED,qBAAa,gBAAgB;IAC3B,OAAO,CAAC,MAAM,CAAgB;IAC9B,OAAO,CAAC,OAAO,CAAS;;IAgBlB,iBAAiB,CAAC,OAAO,EAAE,sBAAsB,GAAG,OAAO,CAAC,uBAAuB,CAAC;IAiCpF,mBAAmB,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,eAAe,EAAE,CAAC;IAgC3E,OAAO,CAAC,kBAAkB;IA8C1B,OAAO,CAAC,eAAe;IAwBvB,OAAO,CAAC,sBAAsB;IAoB9B,OAAO,CAAC,0BAA0B;IAclC,OAAO,CAAC,eAAe;IA4BvB,OAAO,CAAC,iBAAiB;IAazB,OAAO,CAAC,eAAe;IAUvB,OAAO,CAAC,sBAAsB;IAS9B,OAAO,CAAC,gBAAgB;IASlB,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC;CAStC"}