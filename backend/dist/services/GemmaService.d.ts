export interface HealthDomain {
    name: string;
    type: 'neurological' | 'core_property';
    confidence: number;
    description: string;
    relatedProperties: string[];
}
export interface SupplementProperty {
    name: string;
    category: string;
    strength: number;
    evidence: string;
    mechanism?: string;
}
export interface SupplementAnalysis {
    supplementName: string;
    healthDomains: HealthDomain[];
    properties: SupplementProperty[];
    mechanisms: string[];
    interactions: string[];
    safetyProfile: {
        riskLevel: 'low' | 'moderate' | 'high';
        warnings: string[];
        contraindications: string[];
    };
    confidence: number;
    analysisDate: Date;
}
export interface InsightGeneration {
    summary: string;
    keyFindings: string[];
    warnings: string[];
    recommendations: string[];
    confidence: number;
}
export declare class GemmaService {
    private ollama;
    private modelName;
    constructor();
    analyzeSupplementData(supplementName: string, researchData: any): Promise<SupplementAnalysis>;
    generateInsights(supplementName: string, analysis: SupplementAnalysis, graphUpdate: any): Promise<InsightGeneration>;
    processNaturalLanguageQuery(query: string, context: any): Promise<string>;
    private createAnalysisPrompt;
    private createInsightPrompt;
    private createQueryPrompt;
    private parseAnalysisResponse;
    private parseInsightResponse;
    private createFallbackAnalysis;
    private createFallbackInsights;
    healthCheck(): Promise<boolean>;
}
//# sourceMappingURL=GemmaService.d.ts.map