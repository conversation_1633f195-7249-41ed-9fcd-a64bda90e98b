"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GraphService = void 0;
const neo4j_1 = require("@/config/neo4j");
const redis_1 = require("@/config/redis");
const AIService_1 = require("@/services/AIService");
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
const errorHandler_1 = require("@/middleware/errorHandler");
const uuid_1 = require("uuid");
class GraphService {
    aiService;
    constructor() {
        this.aiService = new AIService_1.AIService();
    }
    async getGraphData(filters) {
        const startTime = Date.now();
        const cacheKey = `graph:data:${JSON.stringify(filters)}`;
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                (0, logger_1.logGraphOperation)('Get graph data (cached)', cached.nodes?.length, cached.relationships?.length);
                return cached;
            }
            let nodeQuery = 'MATCH (n)';
            let relationshipQuery = 'MATCH (a)-[r]->(b)';
            const params = {};
            if (filters.nodeTypes && filters.nodeTypes.length > 0) {
                const labels = filters.nodeTypes.map((type, i) => `n:${type}`).join(' OR ');
                nodeQuery += ` WHERE ${labels}`;
            }
            if (filters.search) {
                const searchCondition = filters.nodeTypes && filters.nodeTypes.length > 0 ? ' AND ' : ' WHERE ';
                nodeQuery += `${searchCondition}(n.name CONTAINS $search OR n.description CONTAINS $search)`;
                params.search = filters.search;
            }
            if (filters.relationshipTypes && filters.relationshipTypes.length > 0) {
                const types = filters.relationshipTypes.map(type => `'${type}'`).join(', ');
                relationshipQuery += ` WHERE type(r) IN [${types}]`;
            }
            const limit = Math.min(filters.limit || 1000, environment_1.config.graph.maxNodes);
            nodeQuery += ` RETURN n LIMIT ${limit}`;
            relationshipQuery += ` RETURN a, r, b LIMIT ${Math.min(limit * 5, environment_1.config.graph.maxRelationships)}`;
            const [nodeResult, relationshipResult] = await Promise.all([
                (0, neo4j_1.executeNeo4jQuery)(nodeQuery, params),
                (0, neo4j_1.executeNeo4jQuery)(relationshipQuery, params),
            ]);
            const nodes = nodeResult.records.map(record => {
                const node = record.get('n');
                return {
                    id: node.identity.toString(),
                    labels: node.labels,
                    properties: node.properties,
                };
            });
            const relationships = relationshipResult.records.map(record => {
                const source = record.get('a');
                const relationship = record.get('r');
                const target = record.get('b');
                return {
                    id: relationship.identity.toString(),
                    type: relationship.type,
                    properties: relationship.properties,
                    source: source.identity.toString(),
                    target: target.identity.toString(),
                };
            });
            const result = { nodes, relationships };
            await (0, redis_1.cacheSet)(cacheKey, result, environment_1.config.cache.ttl);
            const duration = Date.now() - startTime;
            (0, logger_1.logGraphOperation)('Get graph data', nodes.length, relationships.length, duration);
            return result;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to get graph data', error, { filters });
            throw new errorHandler_1.DatabaseError('Failed to retrieve graph data');
        }
    }
    async getNodeById(id) {
        const startTime = Date.now();
        const cacheKey = `node:${id}`;
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            const query = 'MATCH (n) WHERE id(n) = $id RETURN n';
            const result = await (0, neo4j_1.executeNeo4jQuery)(query, { id: parseInt(id) });
            if (result.records.length === 0) {
                throw new errorHandler_1.NotFoundError(`Node with id ${id} not found`);
            }
            const node = result.records[0].get('n');
            const nodeData = {
                id: node.identity.toString(),
                labels: node.labels,
                properties: node.properties,
            };
            await (0, redis_1.cacheSet)(cacheKey, nodeData, environment_1.config.cache.ttl);
            const duration = Date.now() - startTime;
            (0, logger_1.logGraphOperation)('Get node by ID', 1, 0, duration);
            return nodeData;
        }
        catch (error) {
            if (error instanceof errorHandler_1.NotFoundError)
                throw error;
            (0, logger_1.logError)('Failed to get node by ID', error, { id });
            throw new errorHandler_1.DatabaseError('Failed to retrieve node');
        }
    }
    async getNodeRelationships(id, filters) {
        const startTime = Date.now();
        const cacheKey = `node:${id}:relationships:${JSON.stringify(filters)}`;
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            let query;
            const params = { id: parseInt(id) };
            switch (filters.direction) {
                case 'incoming':
                    query = 'MATCH (a)-[r]->(n) WHERE id(n) = $id';
                    break;
                case 'outgoing':
                    query = 'MATCH (n)-[r]->(b) WHERE id(n) = $id';
                    break;
                default:
                    query = 'MATCH (n)-[r]-(other) WHERE id(n) = $id';
            }
            if (filters.types && filters.types.length > 0) {
                const types = filters.types.map(type => `'${type}'`).join(', ');
                query += ` AND type(r) IN [${types}]`;
            }
            const limit = Math.min(filters.limit || 100, 1000);
            if (filters.direction === 'incoming') {
                query += ` RETURN a as node, r, n as target LIMIT ${limit}`;
            }
            else if (filters.direction === 'outgoing') {
                query += ` RETURN n as source, r, b as node LIMIT ${limit}`;
            }
            else {
                query += ` RETURN n as center, r, other as node LIMIT ${limit}`;
            }
            const result = await (0, neo4j_1.executeNeo4jQuery)(query, params);
            const relationships = result.records.map(record => {
                const relationship = record.get('r');
                const otherNode = record.get('node') || record.get('target') || record.get('source');
                return {
                    id: relationship.identity.toString(),
                    type: relationship.type,
                    properties: relationship.properties,
                    direction: filters.direction,
                    relatedNode: {
                        id: otherNode.identity.toString(),
                        labels: otherNode.labels,
                        properties: otherNode.properties,
                    },
                };
            });
            await (0, redis_1.cacheSet)(cacheKey, relationships, environment_1.config.cache.ttl);
            const duration = Date.now() - startTime;
            (0, logger_1.logGraphOperation)('Get node relationships', 0, relationships.length, duration);
            return relationships;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to get node relationships', error, { id, filters });
            throw new errorHandler_1.DatabaseError('Failed to retrieve node relationships');
        }
    }
    async searchNodes(searchQuery, filters) {
        const startTime = Date.now();
        const cacheKey = `search:${searchQuery}:${JSON.stringify(filters)}`;
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            let query = `
        CALL db.index.fulltext.queryNodes('supplement_search', $searchQuery) 
        YIELD node, score
        RETURN node, score
        UNION
        CALL db.index.fulltext.queryNodes('ingredient_search', $searchQuery) 
        YIELD node, score
        RETURN node, score
        UNION
        CALL db.index.fulltext.queryNodes('effect_search', $searchQuery) 
        YIELD node, score
        RETURN node, score
        UNION
        CALL db.index.fulltext.queryNodes('study_search', $searchQuery) 
        YIELD node, score
        RETURN node, score
        ORDER BY score DESC
        LIMIT $limit
      `;
            const limit = Math.min(filters.limit || 20, 100);
            const params = {
                searchQuery: `*${searchQuery}*`,
                limit
            };
            let result;
            try {
                result = await (0, neo4j_1.executeNeo4jQuery)(query, params);
            }
            catch (error) {
                query = `
          MATCH (n) 
          WHERE n.name CONTAINS $search 
             OR n.description CONTAINS $search
             OR n.title CONTAINS $search
        `;
                if (filters.nodeTypes && filters.nodeTypes.length > 0) {
                    const labels = filters.nodeTypes.map(type => `n:${type}`).join(' OR ');
                    query += ` AND (${labels})`;
                }
                query += ` RETURN n, 1.0 as score ORDER BY n.name LIMIT $limit`;
                result = await (0, neo4j_1.executeNeo4jQuery)(query, {
                    search: searchQuery,
                    limit
                });
            }
            const nodes = result.records.map(record => {
                const node = record.get('node') || record.get('n');
                const score = record.get('score');
                return {
                    id: node.identity.toString(),
                    labels: node.labels,
                    properties: node.properties,
                    score: typeof score === 'number' ? score : score?.toNumber() || 1.0,
                };
            });
            await (0, redis_1.cacheSet)(cacheKey, nodes, environment_1.config.cache.ttl);
            const duration = Date.now() - startTime;
            (0, logger_1.logGraphOperation)('Search nodes', nodes.length, 0, duration);
            return nodes;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to search nodes', error, { searchQuery, filters });
            throw new errorHandler_1.DatabaseError('Failed to search nodes');
        }
    }
    async createNode(type, properties) {
        const startTime = Date.now();
        try {
            if (!properties.id) {
                properties.id = (0, uuid_1.v4)();
            }
            properties.createdAt = new Date().toISOString();
            properties.updatedAt = new Date().toISOString();
            const query = `
        CREATE (n:${type} $properties)
        RETURN n
      `;
            const result = await (0, neo4j_1.executeNeo4jQuery)(query, { properties });
            const node = result.records[0].get('n');
            const nodeData = {
                id: node.identity.toString(),
                labels: node.labels,
                properties: node.properties,
            };
            await this.invalidateGraphCaches();
            const duration = Date.now() - startTime;
            (0, logger_1.logGraphOperation)('Create node', 1, 0, duration);
            return nodeData;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to create node', error, { type, properties });
            throw new errorHandler_1.DatabaseError('Failed to create node');
        }
    }
    async updateNode(id, properties) {
        const startTime = Date.now();
        try {
            properties.updatedAt = new Date().toISOString();
            const query = `
        MATCH (n) WHERE id(n) = $id
        SET n += $properties
        RETURN n
      `;
            const result = await (0, neo4j_1.executeNeo4jQuery)(query, {
                id: parseInt(id),
                properties
            });
            if (result.records.length === 0) {
                throw new errorHandler_1.NotFoundError(`Node with id ${id} not found`);
            }
            const node = result.records[0].get('n');
            const nodeData = {
                id: node.identity.toString(),
                labels: node.labels,
                properties: node.properties,
            };
            await (0, redis_1.cacheDel)(`node:${id}`);
            await this.invalidateGraphCaches();
            const duration = Date.now() - startTime;
            (0, logger_1.logGraphOperation)('Update node', 1, 0, duration);
            return nodeData;
        }
        catch (error) {
            if (error instanceof errorHandler_1.NotFoundError)
                throw error;
            (0, logger_1.logError)('Failed to update node', error, { id, properties });
            throw new errorHandler_1.DatabaseError('Failed to update node');
        }
    }
    async deleteNode(id) {
        const startTime = Date.now();
        try {
            const query = `
        MATCH (n) WHERE id(n) = $id
        DETACH DELETE n
        RETURN count(n) as deleted
      `;
            const result = await (0, neo4j_1.executeNeo4jQuery)(query, { id: parseInt(id) });
            const deleted = result.records[0].get('deleted').toNumber();
            if (deleted === 0) {
                throw new errorHandler_1.NotFoundError(`Node with id ${id} not found`);
            }
            await (0, redis_1.cacheDel)(`node:${id}`);
            await this.invalidateGraphCaches();
            const duration = Date.now() - startTime;
            (0, logger_1.logGraphOperation)('Delete node', 1, 0, duration);
        }
        catch (error) {
            if (error instanceof errorHandler_1.NotFoundError)
                throw error;
            (0, logger_1.logError)('Failed to delete node', error, { id });
            throw new errorHandler_1.DatabaseError('Failed to delete node');
        }
    }
    async createRelationship(fromId, toId, type, properties = {}) {
        const startTime = Date.now();
        try {
            properties.createdAt = new Date().toISOString();
            properties.updatedAt = new Date().toISOString();
            const query = `
        MATCH (a), (b)
        WHERE id(a) = $fromId AND id(b) = $toId
        CREATE (a)-[r:${type} $properties]->(b)
        RETURN r, a, b
      `;
            const result = await (0, neo4j_1.executeNeo4jQuery)(query, {
                fromId: parseInt(fromId),
                toId: parseInt(toId),
                properties,
            });
            if (result.records.length === 0) {
                throw new errorHandler_1.NotFoundError('One or both nodes not found');
            }
            const relationship = result.records[0].get('r');
            const sourceNode = result.records[0].get('a');
            const targetNode = result.records[0].get('b');
            const relationshipData = {
                id: relationship.identity.toString(),
                type: relationship.type,
                properties: relationship.properties,
                source: {
                    id: sourceNode.identity.toString(),
                    labels: sourceNode.labels,
                    properties: sourceNode.properties,
                },
                target: {
                    id: targetNode.identity.toString(),
                    labels: targetNode.labels,
                    properties: targetNode.properties,
                },
            };
            await this.invalidateGraphCaches();
            const duration = Date.now() - startTime;
            (0, logger_1.logGraphOperation)('Create relationship', 0, 1, duration);
            return relationshipData;
        }
        catch (error) {
            if (error instanceof errorHandler_1.NotFoundError)
                throw error;
            (0, logger_1.logError)('Failed to create relationship', error, { fromId, toId, type, properties });
            throw new errorHandler_1.DatabaseError('Failed to create relationship');
        }
    }
    async extractKnowledgeFromText(text, options) {
        const startTime = Date.now();
        try {
            const extraction = await this.aiService.extractEntitiesAndRelationships(text, {
                entityTypes: this.getEntityTypesForExtraction(options.extractionType),
                confidence: 0.7,
            });
            const createdNodes = [];
            const createdRelationships = [];
            for (const entity of extraction.entities) {
                try {
                    const node = await this.createNode(entity.type, {
                        name: entity.name,
                        description: entity.description,
                        confidence: entity.confidence,
                        source: options.source,
                        extractedFrom: text.substring(0, 200) + '...',
                    });
                    createdNodes.push(node);
                }
                catch (error) {
                    logger_1.logger.warn(`Failed to create node for entity: ${entity.name}`, error);
                }
            }
            for (const relationship of extraction.relationships) {
                try {
                    const sourceNode = createdNodes.find(n => n.properties.name === relationship.source);
                    const targetNode = createdNodes.find(n => n.properties.name === relationship.target);
                    if (sourceNode && targetNode) {
                        const rel = await this.createRelationship(sourceNode.id, targetNode.id, relationship.type, {
                            confidence: relationship.confidence,
                            source: options.source,
                        });
                        createdRelationships.push(rel);
                    }
                }
                catch (error) {
                    logger_1.logger.warn(`Failed to create relationship: ${relationship.type}`, error);
                }
            }
            const duration = Date.now() - startTime;
            (0, logger_1.logGraphOperation)('Extract knowledge', createdNodes.length, createdRelationships.length, duration);
            return {
                extractedEntities: extraction.entities.length,
                extractedRelationships: extraction.relationships.length,
                createdNodes: createdNodes.length,
                createdRelationships: createdRelationships.length,
                nodes: createdNodes,
                relationships: createdRelationships,
            };
        }
        catch (error) {
            (0, logger_1.logError)('Failed to extract knowledge from text', error, { options });
            throw new errorHandler_1.DatabaseError('Failed to extract knowledge from text');
        }
    }
    async expandGraph(nodeId, options) {
        const startTime = Date.now();
        try {
            const node = await this.getNodeById(nodeId);
            const relatedConcepts = await this.aiService.findRelatedConcepts(node.properties.name, {
                type: options.expansionType,
                limit: options.limit,
            });
            const expandedNodes = [];
            const expandedRelationships = [];
            for (const concept of relatedConcepts) {
                try {
                    const existingNodes = await this.searchNodes(concept.name, { limit: 1 });
                    let relatedNode;
                    if (existingNodes.length > 0) {
                        relatedNode = existingNodes[0];
                    }
                    else {
                        relatedNode = await this.createNode(concept.type, {
                            name: concept.name,
                            description: concept.description,
                            confidence: concept.confidence,
                            source: 'AI_EXPANSION',
                        });
                        expandedNodes.push(relatedNode);
                    }
                    const relationship = await this.createRelationship(nodeId, relatedNode.id, concept.relationshipType || 'RELATED_TO', {
                        confidence: concept.confidence,
                        source: 'AI_EXPANSION',
                        expansionType: options.expansionType,
                    });
                    expandedRelationships.push(relationship);
                }
                catch (error) {
                    logger_1.logger.warn(`Failed to expand with concept: ${concept.name}`, error);
                }
            }
            const duration = Date.now() - startTime;
            (0, logger_1.logGraphOperation)('Expand graph', expandedNodes.length, expandedRelationships.length, duration);
            return {
                sourceNode: node,
                expandedNodes,
                expandedRelationships,
                expansionType: options.expansionType,
            };
        }
        catch (error) {
            (0, logger_1.logError)('Failed to expand graph', error, { nodeId, options });
            throw new errorHandler_1.DatabaseError('Failed to expand graph');
        }
    }
    async getGraphStats() {
        const startTime = Date.now();
        const cacheKey = 'graph:stats';
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            const queries = [
                'MATCH (n) RETURN labels(n) as label, count(n) as count',
                'MATCH ()-[r]->() RETURN type(r) as type, count(r) as count',
                'MATCH (n) RETURN count(n) as totalNodes',
                'MATCH ()-[r]->() RETURN count(r) as totalRelationships',
            ];
            const results = await Promise.all(queries.map(query => (0, neo4j_1.executeNeo4jQuery)(query)));
            const nodeStats = results[0].records.map(record => ({
                label: record.get('label')[0] || 'Unknown',
                count: record.get('count').toNumber(),
            }));
            const relationshipStats = results[1].records.map(record => ({
                type: record.get('type'),
                count: record.get('count').toNumber(),
            }));
            const totalNodes = results[2].records[0]?.get('totalNodes').toNumber() || 0;
            const totalRelationships = results[3].records[0]?.get('totalRelationships').toNumber() || 0;
            const stats = {
                totalNodes,
                totalRelationships,
                nodesByType: nodeStats,
                relationshipsByType: relationshipStats,
                lastUpdated: new Date().toISOString(),
            };
            await (0, redis_1.cacheSet)(cacheKey, stats, 300);
            const duration = Date.now() - startTime;
            (0, logger_1.logGraphOperation)('Get graph stats', totalNodes, totalRelationships, duration);
            return stats;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to get graph stats', error);
            throw new errorHandler_1.DatabaseError('Failed to get graph statistics');
        }
    }
    async cleanupGraph(dryRun = true) {
        const startTime = Date.now();
        try {
            const cleanupQueries = [
                'MATCH (n) WHERE NOT (n)--() RETURN n.id as id, labels(n) as labels, n.name as name',
                `MATCH (n) 
         WITH n.name as name, labels(n) as labels, collect(n) as nodes
         WHERE size(nodes) > 1
         RETURN name, labels, nodes`,
                'MATCH ()-[r]->() WHERE r.confidence < 0.5 RETURN id(r) as id, type(r) as type, r.confidence as confidence',
            ];
            const results = await Promise.all(cleanupQueries.map(query => (0, neo4j_1.executeNeo4jQuery)(query)));
            const orphanedNodes = results[0].records.map(record => ({
                id: record.get('id'),
                labels: record.get('labels'),
                name: record.get('name'),
            }));
            const duplicateGroups = results[1].records.map(record => ({
                name: record.get('name'),
                labels: record.get('labels'),
                nodes: record.get('nodes'),
            }));
            const lowConfidenceRelationships = results[2].records.map(record => ({
                id: record.get('id'),
                type: record.get('type'),
                confidence: record.get('confidence'),
            }));
            const cleanupPlan = {
                orphanedNodes: orphanedNodes.length,
                duplicateGroups: duplicateGroups.length,
                lowConfidenceRelationships: lowConfidenceRelationships.length,
                details: {
                    orphanedNodes,
                    duplicateGroups,
                    lowConfidenceRelationships,
                },
            };
            if (!dryRun) {
                const cleanupOperations = [];
                if (orphanedNodes.length > 0) {
                    cleanupOperations.push({
                        query: 'MATCH (n) WHERE NOT (n)--() DELETE n',
                        parameters: {},
                    });
                }
                if (lowConfidenceRelationships.length > 0) {
                    cleanupOperations.push({
                        query: 'MATCH ()-[r]->() WHERE r.confidence < 0.5 DELETE r',
                        parameters: {},
                    });
                }
                if (cleanupOperations.length > 0) {
                    await (0, neo4j_1.executeNeo4jTransaction)(cleanupOperations);
                    await this.invalidateGraphCaches();
                }
            }
            const duration = Date.now() - startTime;
            (0, logger_1.logGraphOperation)('Cleanup graph', 0, 0, duration);
            return {
                dryRun,
                cleanupPlan,
                executed: !dryRun,
            };
        }
        catch (error) {
            (0, logger_1.logError)('Failed to cleanup graph', error, { dryRun });
            throw new errorHandler_1.DatabaseError('Failed to cleanup graph');
        }
    }
    async updateSupplementKnowledge(supplementName, analysisData, researchData) {
        const startTime = Date.now();
        try {
            let supplementNode;
            const existingNodes = await this.searchNodes(supplementName, {
                nodeTypes: ['Supplement'],
                limit: 1
            });
            if (existingNodes.length > 0) {
                supplementNode = await this.updateNode(existingNodes[0].id, {
                    name: supplementName,
                    lastAnalyzed: new Date().toISOString(),
                    confidence: analysisData.confidence || 0.5,
                    analysisVersion: '1.0'
                });
            }
            else {
                supplementNode = await this.createNode('Supplement', {
                    name: supplementName,
                    description: `Supplement: ${supplementName}`,
                    lastAnalyzed: new Date().toISOString(),
                    confidence: analysisData.confidence || 0.5,
                    analysisVersion: '1.0'
                });
            }
            const createdNodes = [];
            const createdRelationships = [];
            if (analysisData.healthDomains) {
                for (const domain of analysisData.healthDomains) {
                    try {
                        let domainNode;
                        const existingDomainNodes = await this.searchNodes(domain.name, {
                            nodeTypes: ['HealthDomain'],
                            limit: 1
                        });
                        if (existingDomainNodes.length > 0) {
                            domainNode = existingDomainNodes[0];
                        }
                        else {
                            domainNode = await this.createNode('HealthDomain', {
                                name: domain.name,
                                type: domain.type,
                                description: domain.description
                            });
                            createdNodes.push(domainNode);
                        }
                        const relationship = await this.createRelationship(supplementNode.id, domainNode.id, 'AFFECTS', {
                            confidence: domain.confidence,
                            source: 'AI_ANALYSIS',
                            relatedProperties: domain.relatedProperties
                        });
                        createdRelationships.push(relationship);
                    }
                    catch (error) {
                        logger_1.logger.warn(`Failed to process health domain: ${domain.name}`, error);
                    }
                }
            }
            if (analysisData.properties) {
                for (const property of analysisData.properties) {
                    try {
                        let propertyNode;
                        const existingPropertyNodes = await this.searchNodes(property.name, {
                            nodeTypes: ['Property'],
                            limit: 1
                        });
                        if (existingPropertyNodes.length > 0) {
                            propertyNode = existingPropertyNodes[0];
                        }
                        else {
                            propertyNode = await this.createNode('Property', {
                                name: property.name,
                                category: property.category,
                                description: `Property: ${property.name}`
                            });
                            createdNodes.push(propertyNode);
                        }
                        const relationship = await this.createRelationship(supplementNode.id, propertyNode.id, 'HAS_PROPERTY', {
                            strength: property.strength,
                            evidence: property.evidence,
                            mechanism: property.mechanism,
                            source: 'AI_ANALYSIS'
                        });
                        createdRelationships.push(relationship);
                    }
                    catch (error) {
                        logger_1.logger.warn(`Failed to process property: ${property.name}`, error);
                    }
                }
            }
            if (researchData && researchData.researchResults) {
                for (const result of researchData.researchResults.slice(0, 5)) {
                    try {
                        const sourceNode = await this.createNode('ResearchSource', {
                            title: result.title,
                            url: result.url,
                            domain: result.domain,
                            relevanceScore: result.relevanceScore,
                            snippet: result.snippet
                        });
                        createdNodes.push(sourceNode);
                        const relationship = await this.createRelationship(supplementNode.id, sourceNode.id, 'SUPPORTED_BY', {
                            relevanceScore: result.relevanceScore,
                            source: 'RESEARCH_DATA'
                        });
                        createdRelationships.push(relationship);
                    }
                    catch (error) {
                        logger_1.logger.warn(`Failed to process research source: ${result.title}`, error);
                    }
                }
            }
            const duration = Date.now() - startTime;
            (0, logger_1.logGraphOperation)('Update supplement knowledge', createdNodes.length, createdRelationships.length, duration);
            return {
                supplementNode,
                createdNodes: createdNodes.length,
                createdRelationships: createdRelationships.length,
                healthDomains: analysisData.healthDomains?.length || 0,
                properties: analysisData.properties?.length || 0,
                researchSources: researchData?.researchResults?.length || 0,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            (0, logger_1.logError)('Failed to update supplement knowledge', error, { supplementName });
            throw new errorHandler_1.DatabaseError('Failed to update supplement knowledge in graph');
        }
    }
    getEntityTypesForExtraction(extractionType) {
        switch (extractionType) {
            case 'supplement':
                return ['Supplement', 'Ingredient', 'Effect', 'Dosage'];
            case 'ingredient':
                return ['Ingredient', 'Effect', 'Interaction', 'Source'];
            case 'study':
                return ['Study', 'Supplement', 'Effect', 'Participant'];
            default:
                return ['Supplement', 'Ingredient', 'Effect', 'Study', 'Interaction'];
        }
    }
    async invalidateGraphCaches() {
        try {
            const graphKeys = await (0, redis_1.cacheGet)('graph:*') || [];
            const nodeKeys = await (0, redis_1.cacheGet)('node:*') || [];
            const searchKeys = await (0, redis_1.cacheGet)('search:*') || [];
            const allKeys = [...graphKeys, ...nodeKeys, ...searchKeys];
            if (allKeys.length > 0) {
                await (0, redis_1.cacheDel)(allKeys);
            }
        }
        catch (error) {
            logger_1.logger.warn('Failed to invalidate graph caches', error);
        }
    }
}
exports.GraphService = GraphService;
//# sourceMappingURL=GraphService.js.map