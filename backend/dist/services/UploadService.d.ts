interface ProcessOptions {
    extractText?: boolean;
    addToKnowledgeBase?: boolean;
    source?: string;
    metadata?: any;
}
interface ListOptions {
    page?: number;
    limit?: number;
    type?: string;
    source?: string;
    search?: string;
}
interface BatchOptions {
    [key: string]: any;
}
export declare class UploadService {
    private ragService;
    private aiService;
    constructor();
    processFile(file: Express.Multer.File, options: ProcessOptions): Promise<any>;
    processFiles(files: Express.Multer.File[], options: ProcessOptions): Promise<any>;
    processUrl(url: string, options: ProcessOptions): Promise<any>;
    getFile(id: string): Promise<any>;
    getFileContent(id: string): Promise<any>;
    fileExists(filePath: string): Promise<boolean>;
    listFiles(options: ListOptions): Promise<any>;
    deleteFile(id: string): Promise<void>;
    batchProcess(fileIds: string[], operation: string, options: BatchOptions): Promise<any>;
    getStats(): Promise<any>;
    private extractTextFromFile;
    private extractTextFromFileId;
    private addToKnowledgeBase;
    private addFileToKnowledgeBase;
}
export {};
//# sourceMappingURL=UploadService.d.ts.map