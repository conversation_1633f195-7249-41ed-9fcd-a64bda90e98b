import { WeaviateClient } from 'weaviate-ts-client';
declare class WeaviateConnection {
    private client;
    private isConnected;
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    getClient(): WeaviateClient;
    private initializeSchemas;
    addObject(className: string, properties: any, id?: string): Promise<string>;
    searchSimilar(className: string, query: string, limit?: number): Promise<any[]>;
    healthCheck(): Promise<boolean>;
    getStats(): Promise<any>;
    clearDatabase(): Promise<void>;
}
declare const weaviateConnection: WeaviateConnection;
export declare const connectWeaviate: () => Promise<void>;
export declare const disconnectWeaviate: () => Promise<void>;
export declare const getWeaviateClient: () => WeaviateClient;
export declare const addWeaviateObject: (className: string, properties: any, id?: string) => Promise<string>;
export declare const searchWeaviateSimilar: (className: string, query: string, limit?: number) => Promise<any[]>;
export declare const weaviateHealthCheck: () => Promise<boolean>;
export declare const getWeaviateStats: () => Promise<any>;
export declare const clearWeaviateDatabase: () => Promise<void>;
export default weaviateConnection;
//# sourceMappingURL=weaviate.d.ts.map