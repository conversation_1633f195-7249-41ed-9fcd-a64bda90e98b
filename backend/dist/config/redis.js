"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRedisStats = exports.redisHealthCheck = exports.cacheLRange = exports.cacheLPush = exports.cacheHDel = exports.cacheHGetAll = exports.cacheHGet = exports.cacheHSet = exports.cacheFlushAll = exports.cacheKeys = exports.cacheTtl = exports.cacheExpire = exports.cacheExists = exports.cacheDel = exports.cacheGet = exports.cacheSet = exports.getRedisClient = exports.disconnectRedis = exports.connectRedis = void 0;
const redis_1 = require("redis");
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
class RedisConnection {
    client = null;
    isConnected = false;
    async connect() {
        try {
            logger_1.logger.info('Connecting to Redis cache...');
            this.client = (0, redis_1.createClient)({
                url: environment_1.config.redis.url,
                password: environment_1.config.redis.password || undefined,
                socket: {
                    connectTimeout: 60000,
                    reconnectStrategy: (retries) => {
                        if (retries > 10) {
                            logger_1.logger.error('Redis reconnection failed after 10 attempts');
                            return false;
                        }
                        return Math.min(retries * 50, 1000);
                    },
                },
            });
            this.client.on('error', (error) => {
                (0, logger_1.logError)('Redis client error', error);
            });
            this.client.on('connect', () => {
                logger_1.logger.info('Redis client connected');
            });
            this.client.on('ready', () => {
                logger_1.logger.info('Redis client ready');
                this.isConnected = true;
            });
            this.client.on('end', () => {
                logger_1.logger.info('Redis client disconnected');
                this.isConnected = false;
            });
            this.client.on('reconnecting', () => {
                logger_1.logger.info('Redis client reconnecting...');
            });
            await this.client.connect();
            logger_1.logger.info('✅ Redis connection established successfully');
        }
        catch (error) {
            (0, logger_1.logError)('❌ Failed to connect to Redis', error);
            throw error;
        }
    }
    async disconnect() {
        if (this.client) {
            await this.client.quit();
            this.isConnected = false;
            logger_1.logger.info('🔌 Redis connection closed');
        }
    }
    getClient() {
        if (!this.client || !this.isConnected) {
            throw new Error('Redis client not initialized. Call connect() first.');
        }
        return this.client;
    }
    async set(key, value, ttl) {
        const client = this.getClient();
        const startTime = Date.now();
        try {
            const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
            if (ttl) {
                await client.setEx(key, ttl, serializedValue);
            }
            else {
                await client.set(key, serializedValue);
            }
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('SET', 'Redis', duration, { key, ttl });
        }
        catch (error) {
            (0, logger_1.logError)('Redis SET failed', error, { key });
            throw error;
        }
    }
    async get(key) {
        const client = this.getClient();
        const startTime = Date.now();
        try {
            const value = await client.get(key);
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('GET', 'Redis', duration, { key, found: !!value });
            if (!value)
                return null;
            try {
                return JSON.parse(value);
            }
            catch {
                return value;
            }
        }
        catch (error) {
            (0, logger_1.logError)('Redis GET failed', error, { key });
            throw error;
        }
    }
    async del(key) {
        const client = this.getClient();
        const startTime = Date.now();
        try {
            const result = await client.del(key);
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('DEL', 'Redis', duration, { key, deleted: result });
            return result;
        }
        catch (error) {
            (0, logger_1.logError)('Redis DEL failed', error, { key });
            throw error;
        }
    }
    async exists(key) {
        const client = this.getClient();
        try {
            const result = await client.exists(key);
            return result === 1;
        }
        catch (error) {
            (0, logger_1.logError)('Redis EXISTS failed', error, { key });
            throw error;
        }
    }
    async expire(key, seconds) {
        const client = this.getClient();
        try {
            const result = await client.expire(key, seconds);
            return result;
        }
        catch (error) {
            (0, logger_1.logError)('Redis EXPIRE failed', error, { key, seconds });
            throw error;
        }
    }
    async ttl(key) {
        const client = this.getClient();
        try {
            return await client.ttl(key);
        }
        catch (error) {
            (0, logger_1.logError)('Redis TTL failed', error, { key });
            throw error;
        }
    }
    async keys(pattern) {
        const client = this.getClient();
        try {
            return await client.keys(pattern);
        }
        catch (error) {
            (0, logger_1.logError)('Redis KEYS failed', error, { pattern });
            throw error;
        }
    }
    async flushAll() {
        if (environment_1.config.isProduction) {
            throw new Error('Cannot flush Redis in production environment');
        }
        const client = this.getClient();
        try {
            await client.flushAll();
            logger_1.logger.warn('⚠️ Redis cache cleared');
        }
        catch (error) {
            (0, logger_1.logError)('Redis FLUSHALL failed', error);
            throw error;
        }
    }
    async hSet(key, field, value) {
        const client = this.getClient();
        try {
            const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
            return await client.hSet(key, field, serializedValue);
        }
        catch (error) {
            (0, logger_1.logError)('Redis HSET failed', error, { key, field });
            throw error;
        }
    }
    async hGet(key, field) {
        const client = this.getClient();
        try {
            const value = await client.hGet(key, field);
            if (!value)
                return null;
            try {
                return JSON.parse(value);
            }
            catch {
                return value;
            }
        }
        catch (error) {
            (0, logger_1.logError)('Redis HGET failed', error, { key, field });
            throw error;
        }
    }
    async hGetAll(key) {
        const client = this.getClient();
        try {
            const hash = await client.hGetAll(key);
            const result = {};
            for (const [field, value] of Object.entries(hash)) {
                try {
                    result[field] = JSON.parse(value);
                }
                catch {
                    result[field] = value;
                }
            }
            return result;
        }
        catch (error) {
            (0, logger_1.logError)('Redis HGETALL failed', error, { key });
            throw error;
        }
    }
    async hDel(key, field) {
        const client = this.getClient();
        try {
            return await client.hDel(key, field);
        }
        catch (error) {
            (0, logger_1.logError)('Redis HDEL failed', error, { key, field });
            throw error;
        }
    }
    async lPush(key, ...values) {
        const client = this.getClient();
        try {
            const serializedValues = values.map(v => typeof v === 'string' ? v : JSON.stringify(v));
            return await client.lPush(key, serializedValues);
        }
        catch (error) {
            (0, logger_1.logError)('Redis LPUSH failed', error, { key });
            throw error;
        }
    }
    async lRange(key, start, stop) {
        const client = this.getClient();
        try {
            const values = await client.lRange(key, start, stop);
            return values.map(value => {
                try {
                    return JSON.parse(value);
                }
                catch {
                    return value;
                }
            });
        }
        catch (error) {
            (0, logger_1.logError)('Redis LRANGE failed', error, { key, start, stop });
            throw error;
        }
    }
    async healthCheck() {
        try {
            const client = this.getClient();
            const result = await client.ping();
            return result === 'PONG';
        }
        catch (error) {
            (0, logger_1.logError)('Redis health check failed', error);
            return false;
        }
    }
    async getStats() {
        try {
            const client = this.getClient();
            const info = await client.info();
            const stats = {};
            const sections = info.split('\r\n\r\n');
            for (const section of sections) {
                const lines = section.split('\r\n');
                if (lines.length > 0 && lines[0]) {
                    const sectionName = lines[0].replace('# ', '');
                    stats[sectionName] = {};
                    for (let i = 1; i < lines.length; i++) {
                        const line = lines[i];
                        if (line && line.includes(':')) {
                            const [key, value] = line.split(':');
                            if (sectionName && key) {
                                stats[sectionName][key] = isNaN(Number(value)) ? value : Number(value);
                            }
                        }
                    }
                }
            }
            return stats;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to get Redis stats', error);
            throw error;
        }
    }
}
const redisConnection = new RedisConnection();
const connectRedis = () => redisConnection.connect();
exports.connectRedis = connectRedis;
const disconnectRedis = () => redisConnection.disconnect();
exports.disconnectRedis = disconnectRedis;
const getRedisClient = () => redisConnection.getClient();
exports.getRedisClient = getRedisClient;
const cacheSet = (key, value, ttl) => redisConnection.set(key, value, ttl);
exports.cacheSet = cacheSet;
const cacheGet = (key) => redisConnection.get(key);
exports.cacheGet = cacheGet;
const cacheDel = (key) => redisConnection.del(key);
exports.cacheDel = cacheDel;
const cacheExists = (key) => redisConnection.exists(key);
exports.cacheExists = cacheExists;
const cacheExpire = (key, seconds) => redisConnection.expire(key, seconds);
exports.cacheExpire = cacheExpire;
const cacheTtl = (key) => redisConnection.ttl(key);
exports.cacheTtl = cacheTtl;
const cacheKeys = (pattern) => redisConnection.keys(pattern);
exports.cacheKeys = cacheKeys;
const cacheFlushAll = () => redisConnection.flushAll();
exports.cacheFlushAll = cacheFlushAll;
const cacheHSet = (key, field, value) => redisConnection.hSet(key, field, value);
exports.cacheHSet = cacheHSet;
const cacheHGet = (key, field) => redisConnection.hGet(key, field);
exports.cacheHGet = cacheHGet;
const cacheHGetAll = (key) => redisConnection.hGetAll(key);
exports.cacheHGetAll = cacheHGetAll;
const cacheHDel = (key, field) => redisConnection.hDel(key, field);
exports.cacheHDel = cacheHDel;
const cacheLPush = (key, ...values) => redisConnection.lPush(key, ...values);
exports.cacheLPush = cacheLPush;
const cacheLRange = (key, start, stop) => redisConnection.lRange(key, start, stop);
exports.cacheLRange = cacheLRange;
const redisHealthCheck = () => redisConnection.healthCheck();
exports.redisHealthCheck = redisHealthCheck;
const getRedisStats = () => redisConnection.getStats();
exports.getRedisStats = getRedisStats;
exports.default = redisConnection;
//# sourceMappingURL=redis.js.map