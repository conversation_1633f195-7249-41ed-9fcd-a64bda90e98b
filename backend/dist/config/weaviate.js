"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearWeaviateDatabase = exports.getWeaviateStats = exports.weaviateHealthCheck = exports.searchWeaviateSimilar = exports.addWeaviateObject = exports.getWeaviateClient = exports.disconnectWeaviate = exports.connectWeaviate = void 0;
const weaviate_ts_client_1 = __importStar(require("weaviate-ts-client"));
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
class WeaviateConnection {
    client = null;
    isConnected = false;
    async connect() {
        try {
            logger_1.logger.info('Connecting to Weaviate vector database...');
            const clientConfig = {
                scheme: environment_1.config.weaviate.url.startsWith('https') ? 'https' : 'http',
                host: environment_1.config.weaviate.url.replace(/^https?:\/\//, ''),
            };
            if (environment_1.config.weaviate.apiKey) {
                clientConfig.apiKey = new weaviate_ts_client_1.ApiKey(environment_1.config.weaviate.apiKey);
            }
            this.client = weaviate_ts_client_1.default.client(clientConfig);
            await this.healthCheck();
            this.isConnected = true;
            logger_1.logger.info('✅ Weaviate connection established successfully');
            await this.initializeSchemas();
        }
        catch (error) {
            (0, logger_1.logError)('❌ Failed to connect to Weaviate', error);
            throw error;
        }
    }
    async disconnect() {
        if (this.client) {
            this.isConnected = false;
            this.client = null;
            logger_1.logger.info('🔌 Weaviate connection closed');
        }
    }
    getClient() {
        if (!this.client || !this.isConnected) {
            throw new Error('Weaviate client not initialized. Call connect() first.');
        }
        return this.client;
    }
    async initializeSchemas() {
        try {
            logger_1.logger.info('Initializing Weaviate schemas...');
            const client = this.getClient();
            const schemas = [
                {
                    class: 'Supplement',
                    description: 'Supplement products and their information',
                    properties: [
                        {
                            name: 'name',
                            dataType: ['text'],
                            description: 'Name of the supplement',
                        },
                        {
                            name: 'description',
                            dataType: ['text'],
                            description: 'Description of the supplement',
                        },
                        {
                            name: 'brand',
                            dataType: ['text'],
                            description: 'Brand or manufacturer',
                        },
                        {
                            name: 'category',
                            dataType: ['text'],
                            description: 'Category of supplement',
                        },
                        {
                            name: 'dosage',
                            dataType: ['text'],
                            description: 'Recommended dosage information',
                        },
                        {
                            name: 'ingredients',
                            dataType: ['text[]'],
                            description: 'List of ingredients',
                        },
                        {
                            name: 'benefits',
                            dataType: ['text[]'],
                            description: 'Claimed benefits',
                        },
                        {
                            name: 'sideEffects',
                            dataType: ['text[]'],
                            description: 'Potential side effects',
                        },
                        {
                            name: 'contraindications',
                            dataType: ['text[]'],
                            description: 'Contraindications and warnings',
                        },
                        {
                            name: 'sourceUrl',
                            dataType: ['text'],
                            description: 'Source URL for the information',
                        },
                        {
                            name: 'lastUpdated',
                            dataType: ['date'],
                            description: 'Last update timestamp',
                        },
                    ],
                    vectorizer: 'text2vec-openai',
                    moduleConfig: {
                        'text2vec-openai': {
                            model: 'ada',
                            modelVersion: '002',
                            type: 'text',
                        },
                    },
                },
                {
                    class: 'Ingredient',
                    description: 'Individual ingredients found in supplements',
                    properties: [
                        {
                            name: 'name',
                            dataType: ['text'],
                            description: 'Name of the ingredient',
                        },
                        {
                            name: 'chemicalName',
                            dataType: ['text'],
                            description: 'Chemical or scientific name',
                        },
                        {
                            name: 'description',
                            dataType: ['text'],
                            description: 'Description of the ingredient',
                        },
                        {
                            name: 'molecularFormula',
                            dataType: ['text'],
                            description: 'Molecular formula',
                        },
                        {
                            name: 'casNumber',
                            dataType: ['text'],
                            description: 'CAS registry number',
                        },
                        {
                            name: 'functions',
                            dataType: ['text[]'],
                            description: 'Biological functions',
                        },
                        {
                            name: 'sources',
                            dataType: ['text[]'],
                            description: 'Natural sources',
                        },
                        {
                            name: 'safetyProfile',
                            dataType: ['text'],
                            description: 'Safety information',
                        },
                        {
                            name: 'interactions',
                            dataType: ['text[]'],
                            description: 'Known interactions',
                        },
                    ],
                    vectorizer: 'text2vec-openai',
                    moduleConfig: {
                        'text2vec-openai': {
                            model: 'ada',
                            modelVersion: '002',
                            type: 'text',
                        },
                    },
                },
                {
                    class: 'Study',
                    description: 'Scientific studies and research papers',
                    properties: [
                        {
                            name: 'title',
                            dataType: ['text'],
                            description: 'Title of the study',
                        },
                        {
                            name: 'abstract',
                            dataType: ['text'],
                            description: 'Study abstract',
                        },
                        {
                            name: 'authors',
                            dataType: ['text[]'],
                            description: 'Study authors',
                        },
                        {
                            name: 'journal',
                            dataType: ['text'],
                            description: 'Journal name',
                        },
                        {
                            name: 'publicationDate',
                            dataType: ['date'],
                            description: 'Publication date',
                        },
                        {
                            name: 'doi',
                            dataType: ['text'],
                            description: 'Digital Object Identifier',
                        },
                        {
                            name: 'pmid',
                            dataType: ['text'],
                            description: 'PubMed ID',
                        },
                        {
                            name: 'studyType',
                            dataType: ['text'],
                            description: 'Type of study (RCT, observational, etc.)',
                        },
                        {
                            name: 'participants',
                            dataType: ['int'],
                            description: 'Number of participants',
                        },
                        {
                            name: 'conclusions',
                            dataType: ['text'],
                            description: 'Study conclusions',
                        },
                        {
                            name: 'qualityScore',
                            dataType: ['number'],
                            description: 'Quality assessment score',
                        },
                    ],
                    vectorizer: 'text2vec-openai',
                    moduleConfig: {
                        'text2vec-openai': {
                            model: 'ada',
                            modelVersion: '002',
                            type: 'text',
                        },
                    },
                },
                {
                    class: 'Effect',
                    description: 'Effects and benefits of supplements',
                    properties: [
                        {
                            name: 'name',
                            dataType: ['text'],
                            description: 'Name of the effect',
                        },
                        {
                            name: 'description',
                            dataType: ['text'],
                            description: 'Description of the effect',
                        },
                        {
                            name: 'category',
                            dataType: ['text'],
                            description: 'Category of effect (cognitive, physical, etc.)',
                        },
                        {
                            name: 'mechanism',
                            dataType: ['text'],
                            description: 'Mechanism of action',
                        },
                        {
                            name: 'evidenceLevel',
                            dataType: ['text'],
                            description: 'Level of scientific evidence',
                        },
                        {
                            name: 'timeToEffect',
                            dataType: ['text'],
                            description: 'Time to see effect',
                        },
                        {
                            name: 'duration',
                            dataType: ['text'],
                            description: 'Duration of effect',
                        },
                    ],
                    vectorizer: 'text2vec-openai',
                    moduleConfig: {
                        'text2vec-openai': {
                            model: 'ada',
                            modelVersion: '002',
                            type: 'text',
                        },
                    },
                },
            ];
            for (const schema of schemas) {
                try {
                    const exists = await client.schema.exists(schema.class);
                    if (!exists) {
                        await client.schema.classCreator().withClass(schema).do();
                        logger_1.logger.info(`✅ Created Weaviate schema: ${schema.class}`);
                    }
                    else {
                        logger_1.logger.info(`ℹ️ Weaviate schema already exists: ${schema.class}`);
                    }
                }
                catch (error) {
                    (0, logger_1.logError)(`Failed to create schema: ${schema.class}`, error);
                    throw error;
                }
            }
            logger_1.logger.info('✅ Weaviate schemas initialized successfully');
        }
        catch (error) {
            (0, logger_1.logError)('❌ Failed to initialize Weaviate schemas', error);
            throw error;
        }
    }
    async addObject(className, properties, id) {
        const client = this.getClient();
        const startTime = Date.now();
        try {
            let creator = client.data.creator().withClassName(className).withProperties(properties);
            if (id) {
                creator = creator.withId(id);
            }
            const result = await creator.do();
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Object added', `Weaviate:${className}`, duration, {
                objectId: result.id,
            });
            return result.id;
        }
        catch (error) {
            (0, logger_1.logError)(`Failed to add object to ${className}`, error, { properties });
            throw error;
        }
    }
    async searchSimilar(className, query, limit = 10) {
        const client = this.getClient();
        const startTime = Date.now();
        try {
            const result = await client.graphql
                .get()
                .withClassName(className)
                .withNearText({ concepts: [query] })
                .withLimit(limit)
                .withFields('_additional { certainty distance } name description')
                .do();
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Similarity search', `Weaviate:${className}`, duration, {
                query,
                resultCount: result.data.Get[className]?.length || 0,
            });
            return result.data.Get[className] || [];
        }
        catch (error) {
            (0, logger_1.logError)(`Failed to search ${className}`, error, { query });
            throw error;
        }
    }
    async healthCheck() {
        try {
            const client = this.getClient();
            const result = await client.misc.liveChecker().do();
            return result === true;
        }
        catch (error) {
            (0, logger_1.logError)('Weaviate health check failed', error);
            return false;
        }
    }
    async getStats() {
        try {
            const client = this.getClient();
            const schema = await client.schema.getter().do();
            const stats = {
                classes: schema.classes?.length || 0,
                classDetails: [],
            };
            if (schema.classes) {
                for (const cls of schema.classes) {
                    try {
                        const result = await client.graphql
                            .aggregate()
                            .withClassName(cls.class)
                            .withFields('meta { count }')
                            .do();
                        stats.classDetails.push({
                            name: cls.class,
                            count: result.data.Aggregate[cls.class]?.[0]?.meta?.count || 0,
                        });
                    }
                    catch (error) {
                        stats.classDetails.push({
                            name: cls.class,
                            count: 0,
                            error: 'Failed to get count',
                        });
                    }
                }
            }
            return stats;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to get Weaviate stats', error);
            throw error;
        }
    }
    async clearDatabase() {
        if (environment_1.config.isProduction) {
            throw new Error('Cannot clear database in production environment');
        }
        try {
            const client = this.getClient();
            const schema = await client.schema.getter().do();
            if (schema.classes) {
                for (const cls of schema.classes) {
                    await client.schema.classDeleter().withClassName(cls.class).do();
                }
            }
            logger_1.logger.warn('⚠️ Weaviate database cleared');
        }
        catch (error) {
            (0, logger_1.logError)('Failed to clear Weaviate database', error);
            throw error;
        }
    }
}
const weaviateConnection = new WeaviateConnection();
const connectWeaviate = () => weaviateConnection.connect();
exports.connectWeaviate = connectWeaviate;
const disconnectWeaviate = () => weaviateConnection.disconnect();
exports.disconnectWeaviate = disconnectWeaviate;
const getWeaviateClient = () => weaviateConnection.getClient();
exports.getWeaviateClient = getWeaviateClient;
const addWeaviateObject = (className, properties, id) => weaviateConnection.addObject(className, properties, id);
exports.addWeaviateObject = addWeaviateObject;
const searchWeaviateSimilar = (className, query, limit) => weaviateConnection.searchSimilar(className, query, limit);
exports.searchWeaviateSimilar = searchWeaviateSimilar;
const weaviateHealthCheck = () => weaviateConnection.healthCheck();
exports.weaviateHealthCheck = weaviateHealthCheck;
const getWeaviateStats = () => weaviateConnection.getStats();
exports.getWeaviateStats = getWeaviateStats;
const clearWeaviateDatabase = () => weaviateConnection.clearDatabase();
exports.clearWeaviateDatabase = clearWeaviateDatabase;
exports.default = weaviateConnection;
//# sourceMappingURL=weaviate.js.map