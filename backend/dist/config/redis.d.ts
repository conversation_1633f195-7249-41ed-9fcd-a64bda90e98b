import { RedisClientType } from 'redis';
declare class RedisConnection {
    private client;
    private isConnected;
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    getClient(): RedisClientType;
    set(key: string, value: any, ttl?: number): Promise<void>;
    get(key: string): Promise<any>;
    del(key: string | string[]): Promise<number>;
    exists(key: string): Promise<boolean>;
    expire(key: string, seconds: number): Promise<boolean>;
    ttl(key: string): Promise<number>;
    keys(pattern: string): Promise<string[]>;
    flushAll(): Promise<void>;
    hSet(key: string, field: string, value: any): Promise<number>;
    hGet(key: string, field: string): Promise<any>;
    hGetAll(key: string): Promise<Record<string, any>>;
    hDel(key: string, field: string | string[]): Promise<number>;
    lPush(key: string, ...values: any[]): Promise<number>;
    lRange(key: string, start: number, stop: number): Promise<any[]>;
    healthCheck(): Promise<boolean>;
    getStats(): Promise<any>;
}
declare const redisConnection: RedisConnection;
export declare const connectRedis: () => Promise<void>;
export declare const disconnectRedis: () => Promise<void>;
export declare const getRedisClient: () => RedisClientType;
export declare const cacheSet: (key: string, value: any, ttl?: number) => Promise<void>;
export declare const cacheGet: (key: string) => Promise<any>;
export declare const cacheDel: (key: string | string[]) => Promise<number>;
export declare const cacheExists: (key: string) => Promise<boolean>;
export declare const cacheExpire: (key: string, seconds: number) => Promise<boolean>;
export declare const cacheTtl: (key: string) => Promise<number>;
export declare const cacheKeys: (pattern: string) => Promise<string[]>;
export declare const cacheFlushAll: () => Promise<void>;
export declare const cacheHSet: (key: string, field: string, value: any) => Promise<number>;
export declare const cacheHGet: (key: string, field: string) => Promise<any>;
export declare const cacheHGetAll: (key: string) => Promise<Record<string, any>>;
export declare const cacheHDel: (key: string, field: string | string[]) => Promise<number>;
export declare const cacheLPush: (key: string, ...values: any[]) => Promise<number>;
export declare const cacheLRange: (key: string, start: number, stop: number) => Promise<any[]>;
export declare const redisHealthCheck: () => Promise<boolean>;
export declare const getRedisStats: () => Promise<any>;
export default redisConnection;
//# sourceMappingURL=redis.d.ts.map