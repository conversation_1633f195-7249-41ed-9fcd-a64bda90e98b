"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.mongoose = exports.clearMongoDBDatabase = exports.getMongoDBStats = exports.mongoDBHealthCheck = exports.getMongoDBConnection = exports.disconnectMongoDB = exports.connectMongoDB = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
exports.mongoose = mongoose_1.default;
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
class MongoDBConnection {
    isConnected = false;
    async connect() {
        try {
            logger_1.logger.info('Connecting to MongoDB...');
            mongoose_1.default.set('strictQuery', false);
            await mongoose_1.default.connect(environment_1.config.mongodb.uri, {
                maxPoolSize: 10,
                serverSelectionTimeoutMS: 5000,
                socketTimeoutMS: 45000,
                bufferCommands: false,
            });
            this.isConnected = true;
            mongoose_1.default.connection.on('connected', () => {
                logger_1.logger.info('✅ MongoDB connected successfully');
            });
            mongoose_1.default.connection.on('error', (error) => {
                (0, logger_1.logError)('MongoDB connection error', error);
            });
            mongoose_1.default.connection.on('disconnected', () => {
                logger_1.logger.warn('MongoDB disconnected');
                this.isConnected = false;
            });
            mongoose_1.default.connection.on('reconnected', () => {
                logger_1.logger.info('MongoDB reconnected');
                this.isConnected = true;
            });
            process.on('SIGINT', async () => {
                await this.disconnect();
                process.exit(0);
            });
            logger_1.logger.info('✅ MongoDB connection established successfully');
        }
        catch (error) {
            (0, logger_1.logError)('❌ Failed to connect to MongoDB', error);
            throw error;
        }
    }
    async disconnect() {
        if (this.isConnected) {
            await mongoose_1.default.connection.close();
            this.isConnected = false;
            logger_1.logger.info('🔌 MongoDB connection closed');
        }
    }
    getConnection() {
        if (!this.isConnected) {
            throw new Error('MongoDB not connected. Call connect() first.');
        }
        return mongoose_1.default.connection;
    }
    async healthCheck() {
        try {
            const state = mongoose_1.default.connection.readyState;
            return state === 1;
        }
        catch (error) {
            (0, logger_1.logError)('MongoDB health check failed', error);
            return false;
        }
    }
    async getStats() {
        try {
            const db = mongoose_1.default.connection.db;
            if (!db) {
                throw new Error('Database not available');
            }
            const stats = await db.stats();
            const collections = await db.listCollections().toArray();
            const collectionStats = [];
            for (const collection of collections) {
                try {
                    const collStats = await db.collection(collection.name).estimatedDocumentCount();
                    collectionStats.push({
                        name: collection.name,
                        count: collStats,
                        size: 0,
                        avgObjSize: 0,
                        indexes: 0,
                    });
                }
                catch (error) {
                    collectionStats.push({
                        name: collection.name,
                        error: 'Stats not available',
                    });
                }
            }
            return {
                database: {
                    name: stats['db'],
                    collections: stats['collections'],
                    objects: stats['objects'],
                    dataSize: stats['dataSize'],
                    storageSize: stats['storageSize'],
                    indexes: stats['indexes'],
                    indexSize: stats['indexSize'],
                },
                collections: collectionStats,
            };
        }
        catch (error) {
            (0, logger_1.logError)('Failed to get MongoDB stats', error);
            throw error;
        }
    }
    async clearDatabase() {
        if (environment_1.config.isProduction) {
            throw new Error('Cannot clear database in production environment');
        }
        try {
            const db = mongoose_1.default.connection.db;
            if (!db) {
                throw new Error('Database not available');
            }
            const collections = await db.listCollections().toArray();
            for (const collection of collections) {
                await db.collection(collection.name).deleteMany({});
            }
            logger_1.logger.warn('⚠️ MongoDB database cleared');
        }
        catch (error) {
            (0, logger_1.logError)('Failed to clear MongoDB database', error);
            throw error;
        }
    }
}
const mongoDBConnection = new MongoDBConnection();
const connectMongoDB = () => mongoDBConnection.connect();
exports.connectMongoDB = connectMongoDB;
const disconnectMongoDB = () => mongoDBConnection.disconnect();
exports.disconnectMongoDB = disconnectMongoDB;
const getMongoDBConnection = () => mongoDBConnection.getConnection();
exports.getMongoDBConnection = getMongoDBConnection;
const mongoDBHealthCheck = () => mongoDBConnection.healthCheck();
exports.mongoDBHealthCheck = mongoDBHealthCheck;
const getMongoDBStats = () => mongoDBConnection.getStats();
exports.getMongoDBStats = getMongoDBStats;
const clearMongoDBDatabase = () => mongoDBConnection.clearDatabase();
exports.clearMongoDBDatabase = clearMongoDBDatabase;
exports.default = mongoDBConnection;
//# sourceMappingURL=mongodb.js.map