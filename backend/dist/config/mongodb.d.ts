import mongoose from 'mongoose';
declare class MongoDBConnection {
    private isConnected;
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    getConnection(): typeof mongoose.connection;
    healthCheck(): Promise<boolean>;
    getStats(): Promise<any>;
    clearDatabase(): Promise<void>;
}
declare const mongoDBConnection: MongoDBConnection;
export declare const connectMongoDB: () => Promise<void>;
export declare const disconnectMongoDB: () => Promise<void>;
export declare const getMongoDBConnection: () => mongoose.Connection;
export declare const mongoDBHealthCheck: () => Promise<boolean>;
export declare const getMongoDBStats: () => Promise<any>;
export declare const clearMongoDBDatabase: () => Promise<void>;
export { mongoose };
export default mongoDBConnection;
//# sourceMappingURL=mongodb.d.ts.map