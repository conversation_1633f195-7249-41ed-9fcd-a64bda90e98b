"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearNeo4jDatabase = exports.getNeo4jStats = exports.neo4jHealthCheck = exports.executeNeo4jTransaction = exports.executeNeo4jQuery = exports.getNeo4jSession = exports.getNeo4jDriver = exports.disconnectNeo4j = exports.connectNeo4j = void 0;
const neo4j_driver_1 = __importStar(require("neo4j-driver"));
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
class Neo4jConnection {
    driver = null;
    isConnected = false;
    async connect() {
        try {
            logger_1.logger.info('Connecting to Neo4j database...');
            this.driver = neo4j_driver_1.default.driver(environment_1.config.neo4j.uri, neo4j_driver_1.auth.basic(environment_1.config.neo4j.username, environment_1.config.neo4j.password), {
                maxConnectionLifetime: 3 * 60 * 60 * 1000,
                maxConnectionPoolSize: 50,
                connectionAcquisitionTimeout: 2 * 60 * 1000,
                disableLosslessIntegers: true,
            });
            await this.driver.verifyConnectivity();
            this.isConnected = true;
            logger_1.logger.info('✅ Neo4j connection established successfully');
            await this.initializeSchema();
        }
        catch (error) {
            (0, logger_1.logError)('❌ Failed to connect to Neo4j', error);
            throw error;
        }
    }
    async disconnect() {
        if (this.driver) {
            await this.driver.close();
            this.isConnected = false;
            logger_1.logger.info('🔌 Neo4j connection closed');
        }
    }
    getDriver() {
        if (!this.driver || !this.isConnected) {
            throw new Error('Neo4j driver not initialized. Call connect() first.');
        }
        return this.driver;
    }
    async getSession() {
        const driver = this.getDriver();
        return driver.session();
    }
    async executeQuery(query, parameters = {}) {
        const session = await this.getSession();
        const startTime = Date.now();
        try {
            const result = await session.run(query, parameters);
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Query executed', 'Neo4j', duration, {
                query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
                recordCount: result.records.length,
            });
            return result;
        }
        catch (error) {
            (0, logger_1.logError)('Neo4j query failed', error, { query, parameters });
            throw error;
        }
        finally {
            await session.close();
        }
    }
    async executeTransaction(queries) {
        const session = await this.getSession();
        const startTime = Date.now();
        try {
            const results = await session.executeWrite(async (tx) => {
                const txResults = [];
                for (const { query, parameters = {} } of queries) {
                    const result = await tx.run(query, parameters);
                    txResults.push(result);
                }
                return txResults;
            });
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Transaction executed', 'Neo4j', duration, {
                queryCount: queries.length,
            });
            return results;
        }
        catch (error) {
            (0, logger_1.logError)('Neo4j transaction failed', error, { queryCount: queries.length });
            throw error;
        }
        finally {
            await session.close();
        }
    }
    async initializeSchema() {
        try {
            logger_1.logger.info('Initializing Neo4j schema...');
            const schemaQueries = [
                'CREATE CONSTRAINT supplement_id IF NOT EXISTS FOR (s:Supplement) REQUIRE s.id IS UNIQUE',
                'CREATE CONSTRAINT ingredient_id IF NOT EXISTS FOR (i:Ingredient) REQUIRE i.id IS UNIQUE',
                'CREATE CONSTRAINT effect_id IF NOT EXISTS FOR (e:Effect) REQUIRE e.id IS UNIQUE',
                'CREATE CONSTRAINT study_id IF NOT EXISTS FOR (st:Study) REQUIRE st.id IS UNIQUE',
                'CREATE CONSTRAINT condition_id IF NOT EXISTS FOR (c:Condition) REQUIRE c.id IS UNIQUE',
                'CREATE CONSTRAINT interaction_id IF NOT EXISTS FOR (int:Interaction) REQUIRE int.id IS UNIQUE',
                'CREATE INDEX supplement_name IF NOT EXISTS FOR (s:Supplement) ON (s.name)',
                'CREATE INDEX ingredient_name IF NOT EXISTS FOR (i:Ingredient) ON (i.name)',
                'CREATE INDEX effect_name IF NOT EXISTS FOR (e:Effect) ON (e.name)',
                'CREATE INDEX study_title IF NOT EXISTS FOR (st:Study) ON (st.title)',
                'CREATE INDEX condition_name IF NOT EXISTS FOR (c:Condition) ON (c.name)',
                'CREATE FULLTEXT INDEX supplement_search IF NOT EXISTS FOR (s:Supplement) ON EACH [s.name, s.description, s.brand]',
                'CREATE FULLTEXT INDEX ingredient_search IF NOT EXISTS FOR (i:Ingredient) ON EACH [i.name, i.description, i.chemicalName]',
                'CREATE FULLTEXT INDEX effect_search IF NOT EXISTS FOR (e:Effect) ON EACH [e.name, e.description]',
                'CREATE FULLTEXT INDEX study_search IF NOT EXISTS FOR (st:Study) ON EACH [st.title, st.abstract, st.authors]',
            ];
            for (const query of schemaQueries) {
                try {
                    await this.executeQuery(query);
                }
                catch (error) {
                    if (!error.message.includes('already exists') && !error.message.includes('An equivalent')) {
                        throw error;
                    }
                }
            }
            logger_1.logger.info('✅ Neo4j schema initialized successfully');
        }
        catch (error) {
            (0, logger_1.logError)('❌ Failed to initialize Neo4j schema', error);
            throw error;
        }
    }
    async healthCheck() {
        try {
            const result = await this.executeQuery('RETURN 1 as health');
            return result.records.length > 0;
        }
        catch (error) {
            (0, logger_1.logError)('Neo4j health check failed', error);
            return false;
        }
    }
    async getStats() {
        try {
            const queries = [
                'MATCH (n) RETURN labels(n) as label, count(n) as count',
                'MATCH ()-[r]->() RETURN type(r) as relationship, count(r) as count',
                'CALL db.stats.retrieve("GRAPH COUNTS")',
            ];
            const results = await Promise.all(queries.map(query => this.executeQuery(query)));
            return {
                nodes: results[0].records.map(record => ({
                    label: record.get('label'),
                    count: record.get('count').toNumber(),
                })),
                relationships: results[1].records.map(record => ({
                    type: record.get('relationship'),
                    count: record.get('count').toNumber(),
                })),
                graphCounts: results[2].records[0]?.toObject() || {},
            };
        }
        catch (error) {
            (0, logger_1.logError)('Failed to get Neo4j stats', error);
            throw error;
        }
    }
    async clearDatabase() {
        if (environment_1.config.isProduction) {
            throw new Error('Cannot clear database in production environment');
        }
        try {
            await this.executeQuery('MATCH (n) DETACH DELETE n');
            logger_1.logger.warn('⚠️ Neo4j database cleared');
        }
        catch (error) {
            (0, logger_1.logError)('Failed to clear Neo4j database', error);
            throw error;
        }
    }
}
const neo4jConnection = new Neo4jConnection();
const connectNeo4j = () => neo4jConnection.connect();
exports.connectNeo4j = connectNeo4j;
const disconnectNeo4j = () => neo4jConnection.disconnect();
exports.disconnectNeo4j = disconnectNeo4j;
const getNeo4jDriver = () => neo4jConnection.getDriver();
exports.getNeo4jDriver = getNeo4jDriver;
const getNeo4jSession = () => neo4jConnection.getSession();
exports.getNeo4jSession = getNeo4jSession;
const executeNeo4jQuery = (query, parameters) => neo4jConnection.executeQuery(query, parameters);
exports.executeNeo4jQuery = executeNeo4jQuery;
const executeNeo4jTransaction = (queries) => neo4jConnection.executeTransaction(queries);
exports.executeNeo4jTransaction = executeNeo4jTransaction;
const neo4jHealthCheck = () => neo4jConnection.healthCheck();
exports.neo4jHealthCheck = neo4jHealthCheck;
const getNeo4jStats = () => neo4jConnection.getStats();
exports.getNeo4jStats = getNeo4jStats;
const clearNeo4jDatabase = () => neo4jConnection.clearDatabase();
exports.clearNeo4jDatabase = clearNeo4jDatabase;
exports.default = neo4jConnection;
//# sourceMappingURL=neo4j.js.map