export declare const config: {
    readonly nodeEnv: string;
    readonly isDevelopment: boolean;
    readonly isProduction: boolean;
    readonly isTest: boolean;
    readonly port: number;
    readonly neo4j: {
        readonly uri: string;
        readonly username: string;
        readonly password: string;
    };
    readonly weaviate: {
        readonly url: string;
        readonly apiKey: string | undefined;
    };
    readonly redis: {
        readonly url: string;
        readonly password: string | undefined;
    };
    readonly mongodb: {
        readonly uri: string;
    };
    readonly ai: {
        readonly geminiApiKey: string;
        readonly openaiApiKey: string | undefined;
        readonly anthropicApiKey: string | undefined;
        readonly ollamaUrl: string | undefined;
        readonly defaultModel: string;
        readonly maxTokens: number;
        readonly temperature: number;
    };
    readonly research: {
        readonly braveApiKey: string | undefined;
        readonly tavilyApiKey: string | undefined;
        readonly gemmaMedicalUrl: string;
    };
    readonly jwt: {
        readonly secret: string;
        readonly expiresIn: string;
    };
    readonly corsOrigins: string[];
    readonly upload: {
        readonly maxFileSize: number;
        readonly uploadDir: string;
    };
    readonly rateLimit: {
        readonly windowMs: number;
        readonly maxRequests: number;
    };
    readonly logging: {
        readonly level: string;
        readonly file: string;
    };
    readonly cache: {
        readonly ttl: number;
    };
    readonly graph: {
        readonly maxNodes: number;
        readonly maxRelationships: number;
        readonly expansionLimit: number;
    };
    readonly rag: {
        readonly embeddingModel: string;
        readonly vectorDimension: number;
        readonly similarityThreshold: number;
        readonly maxContextLength: number;
    };
    readonly crawling: {
        readonly delayMs: number;
        readonly maxDepth: number;
        readonly maxPagesPerDomain: number;
    };
    readonly healthCheck: {
        readonly interval: number;
    };
};
export type Config = typeof config;
export type NodeEnv = typeof config.nodeEnv;
//# sourceMappingURL=environment.d.ts.map