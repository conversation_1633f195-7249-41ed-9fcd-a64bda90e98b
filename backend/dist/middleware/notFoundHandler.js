"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.notFoundHandler = void 0;
const errorHandler_1 = require("@/middleware/errorHandler");
const notFoundHandler = (req, res, next) => {
    const err = new errorHandler_1.NotFoundError(`Can't find ${req.originalUrl} on this server!`);
    next(err);
};
exports.notFoundHandler = notFoundHandler;
//# sourceMappingURL=notFoundHandler.js.map