"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const compression_1 = __importDefault(require("compression"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const dotenv_1 = __importDefault(require("dotenv"));
const http_1 = require("http");
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
const errorHandler_1 = require("@/middleware/errorHandler");
const notFoundHandler_1 = require("@/middleware/notFoundHandler");
const requestLogger_1 = require("@/middleware/requestLogger");
const neo4j_1 = require("@/config/neo4j");
const weaviate_1 = require("@/config/weaviate");
const redis_1 = require("@/config/redis");
const mongodb_1 = require("@/config/mongodb");
const graph_1 = require("@/routes/graph");
const rag_1 = require("@/routes/rag");
const ai_1 = require("@/routes/ai");
const health_1 = require("@/routes/health");
const upload_1 = require("@/routes/upload");
const research_1 = __importDefault(require("@/routes/research"));
const agui_1 = __importStar(require("@/routes/agui"));
dotenv_1.default.config();
class Application {
    app;
    server;
    port;
    constructor() {
        this.app = (0, express_1.default)();
        this.port = environment_1.config.port;
        this.initializeMiddleware();
        this.initializeRoutes();
        this.initializeErrorHandling();
    }
    initializeMiddleware() {
        this.app.use((0, helmet_1.default)({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                },
            },
        }));
        this.app.use((0, cors_1.default)({
            origin: environment_1.config.corsOrigins,
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
        }));
        const limiter = (0, express_rate_limit_1.default)({
            windowMs: 15 * 60 * 1000,
            max: environment_1.config.isDevelopment ? 1000 : 100,
            message: 'Too many requests from this IP, please try again later.',
            standardHeaders: true,
            legacyHeaders: false,
        });
        this.app.use('/api/', limiter);
        this.app.use(express_1.default.json({ limit: '50mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '50mb' }));
        this.app.use((0, compression_1.default)());
        if (environment_1.config.isDevelopment) {
            this.app.use((0, morgan_1.default)('dev'));
        }
        else {
            this.app.use((0, morgan_1.default)('combined', { stream: { write: (message) => logger_1.logger.info(message.trim()) } }));
        }
        this.app.use(requestLogger_1.requestLogger);
        this.app.get('/health', (_req, res) => {
            res.status(200).json({
                status: 'OK',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                environment: environment_1.config.nodeEnv,
            });
        });
    }
    initializeRoutes() {
        this.app.use('/api/health', health_1.healthRoutes);
        this.app.use('/api/graph', graph_1.graphRoutes);
        this.app.use('/api/rag', rag_1.ragRoutes);
        this.app.use('/api/ai', ai_1.aiRoutes);
        this.app.use('/api/upload', upload_1.uploadRoutes);
        this.app.use('/api/research', research_1.default);
        this.app.use('/agui', agui_1.default);
        this.app.get('/api', (_req, res) => {
            res.json({
                name: 'Suplementor Knowledge Graph API',
                version: '1.0.0',
                description: 'API for managing supplement knowledge graphs with AI integration',
                endpoints: {
                    health: '/api/health',
                    graph: '/api/graph',
                    rag: '/api/rag',
                    ai: '/api/ai',
                    upload: '/api/upload',
                    research: '/api/research',
                    agui: '/agui',
                },
                websockets: {
                    agui: '/agui/ws',
                },
                documentation: '/api/docs',
            });
        });
    }
    initializeErrorHandling() {
        this.app.use(notFoundHandler_1.notFoundHandler);
        this.app.use(errorHandler_1.errorHandler);
    }
    async initializeDatabases() {
        try {
            logger_1.logger.info('Initializing database connections...');
            await (0, neo4j_1.connectNeo4j)();
            logger_1.logger.info('✅ Neo4j connected successfully');
            await (0, weaviate_1.connectWeaviate)();
            logger_1.logger.info('✅ Weaviate connected successfully');
            await (0, redis_1.connectRedis)();
            logger_1.logger.info('✅ Redis connected successfully');
            await (0, mongodb_1.connectMongoDB)();
            logger_1.logger.info('✅ MongoDB connected successfully');
            logger_1.logger.info('🚀 All databases connected successfully');
        }
        catch (error) {
            logger_1.logger.error('❌ Database connection failed:', error);
            throw error;
        }
    }
    async start() {
        try {
            await this.initializeDatabases();
            this.server = (0, http_1.createServer)(this.app);
            (0, agui_1.initializeAGUIWebSocket)(this.server);
            this.server.listen(this.port, () => {
                logger_1.logger.info(`🚀 Server running on port ${this.port}`);
                logger_1.logger.info(`📊 Environment: ${environment_1.config.nodeEnv}`);
                logger_1.logger.info(`🔗 API URL: http://localhost:${this.port}/api`);
                logger_1.logger.info(`🔌 AG-UI WebSocket: ws://localhost:${this.port}/agui/ws`);
                logger_1.logger.info(`💚 Health check: http://localhost:${this.port}/health`);
            });
            this.setupGracefulShutdown();
        }
        catch (error) {
            logger_1.logger.error('❌ Failed to start server:', error);
            process.exit(1);
        }
    }
    setupGracefulShutdown() {
        const gracefulShutdown = async (signal) => {
            logger_1.logger.info(`📴 Received ${signal}. Starting graceful shutdown...`);
            if (this.server) {
                this.server.close(async () => {
                    logger_1.logger.info('🔌 HTTP server closed');
                    try {
                        logger_1.logger.info('🔌 Database connections closed');
                        logger_1.logger.info('✅ Graceful shutdown completed');
                        process.exit(0);
                    }
                    catch (error) {
                        logger_1.logger.error('❌ Error during graceful shutdown:', error);
                        process.exit(1);
                    }
                });
            }
            else {
                process.exit(0);
            }
        };
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
        process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2'));
        process.on('uncaughtException', (error) => {
            logger_1.logger.error('❌ Uncaught Exception:', error);
            gracefulShutdown('uncaughtException');
        });
        process.on('unhandledRejection', (reason, promise) => {
            logger_1.logger.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
            gracefulShutdown('unhandledRejection');
        });
    }
}
const app = new Application();
app.start().catch((error) => {
    logger_1.logger.error('❌ Failed to start application:', error);
    process.exit(1);
});
exports.default = app;
//# sourceMappingURL=index.js.map