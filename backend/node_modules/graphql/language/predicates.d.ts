import type {
  ASTNode,
  ConstV<PERSON>ueN<PERSON>,
  DefinitionNode,
  ExecutableDefinitionN<PERSON>,
  SelectionNode,
  TypeDefinitionNode,
  TypeExtensionNode,
  TypeNode,
  TypeSystemDefinitionNode,
  TypeSystemExtensionNode,
  ValueNode,
} from './ast';
export declare function isDefinitionNode(node: ASTNode): node is DefinitionNode;
export declare function isExecutableDefinitionNode(
  node: ASTNode,
): node is ExecutableDefinitionNode;
export declare function isSelectionNode(node: ASTNode): node is SelectionNode;
export declare function isValueNode(node: ASTNode): node is ValueNode;
export declare function isConstValueNode(node: ASTNode): node is ConstValueNode;
export declare function isTypeNode(node: ASTNode): node is TypeNode;
export declare function isTypeSystemDefinitionNode(
  node: ASTNode,
): node is TypeSystemDefinitionNode;
export declare function isTypeDefinitionNode(
  node: ASTNode,
): node is TypeDefinitionNode;
export declare function isTypeSystemExtensionNode(
  node: ASTNode,
): node is TypeSystemExtensionNode;
export declare function isTypeExtensionNode(
  node: ASTNode,
): node is TypeExtensionNode;
