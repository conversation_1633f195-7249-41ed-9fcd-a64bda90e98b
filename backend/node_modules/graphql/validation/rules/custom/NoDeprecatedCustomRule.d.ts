import type { ASTVisitor } from '../../../language/visitor';
import type { ValidationContext } from '../../ValidationContext';
/**
 * No deprecated
 *
 * A GraphQL document is only valid if all selected fields and all used enum values have not been
 * deprecated.
 *
 * Note: This rule is optional and is not part of the Validation section of the GraphQL
 * Specification. The main purpose of this rule is detection of deprecated usages and not
 * necessarily to forbid their use when querying a service.
 */
export declare function NoDeprecatedCustomRule(
  context: ValidationContext,
): ASTVisitor;
